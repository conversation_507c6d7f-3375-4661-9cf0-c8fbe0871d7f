{"timestamp": "2025-06-26T07:02:49.561993", "rag_server_url": "http://home-ai-server:5002", "test_results": [{"collection_name": "utils", "basic_test": {"collection_name": "utils", "queries_tested": 10, "successful_queries": 10, "failed_queries": 0, "query_results": [{"query": "function definition", "results_count": 3, "fixed": true, "preview": "🔍 **Codebase**: utils\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -344.558)\n📁 **File**: `tmwtimer.c` | **Language**: C | **Type**: function `tmwtimer_applInit()`\n\n```c\nFile: tmwtimer...."}, {"query": "class declaration", "results_count": 3, "preview": "🔍 **Codebase**: utils\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -358.568)\n📁 **File**: `tmwsim.c` | **Language**: C | **Type**: function `tmwsim_getEventClass()`\n\n```c\nFile: tmwsim.c..."}, {"query": "error handling", "results_count": 3, "preview": "🔍 **Codebase**: utils\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -336.037)\n📁 **File**: `tmwdiag.c` | **Language**: C | **Type**: function `tmwdiag_error()`\n\n```c\nFile: tmwdiag.c | La..."}, {"query": "memory allocation", "results_count": 3, "preview": "🔍 **Codebase**: utils\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -388.678)\n📁 **File**: `tmwsim.c` | **Language**: C | **Type**: function `tmwsim_setBinaryTime()`\n\n```c\nFile: tmwsim.c..."}, {"query": "data structure", "results_count": 3, "preview": "🔍 **Codebase**: utils\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -360.794)\n📁 **File**: `tmwcrypto.c` | **Language**: C | **Type**: struct_specifier\n\n```c\nFile: tmwcrypto.c | Language..."}, {"query": "pointer arithmetic", "results_count": 3, "preview": "🔍 **Codebase**: utils\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -392.591)\n📁 **File**: `tmwsim.c` | **Language**: C | **Type**: function `tmwsim_initBitstring()`\n\n```c\nFile: tmwsim.c..."}, {"query": "struct definition", "results_count": 3, "preview": "🔍 **Codebase**: utils\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -275.247)\n📁 **File**: `tmwsim.h` | **Language**: C | **Type**: struct_specifier\n\n```c\nFile: tmwsim.h | Language: c | ..."}, {"query": "template class", "results_count": 3, "preview": "🔍 **Codebase**: utils\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -393.854)\n📁 **File**: `tmwdlist.h` | **Language**: C | **Type**: struct_specifier\n\n```c\nFile: tmwdlist.h | Language: ..."}, {"query": "namespace std", "results_count": 3, "preview": "🔍 **Codebase**: utils\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -341.525)\n📁 **File**: `tmwvrsn.cs` | **Language**: CSHARP | **Type**: namespace\n\n```csharp\nFile: tmwvrsn.cs | Languag..."}, {"query": "malloc free", "results_count": 3, "preview": "🔍 **Codebase**: utils\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -477.320)\n📁 **File**: `tmwappl.c` | **Language**: C | **Type**: function `unknown()`\n\n```c\nFile: tmwappl.c | Language..."}], "success": true}, "ai_test": {"questions_tested": 3, "successful_answers": 3, "results": [{"question": "What encryption functions are available?", "success": true, "response_length": 2004}, {"question": "How is memory management handled?", "success": true, "response_length": 2189}, {"question": "What are the main classes and their purposes?", "success": true, "response_length": 1649}]}, "performance": {"average_time": 0.09595489501953125, "min_time": 0.06511235237121582, "max_time": 0.1547389030456543, "queries_tested": 5}}, {"collection_name": "test_project", "basic_test": {"collection_name": "test_project", "queries_tested": 10, "successful_queries": 10, "failed_queries": 0, "query_results": [{"query": "function definition", "results_count": 3, "preview": "🔍 **Codebase**: test_project\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -0.216)\n📁 **File**: `main.cpp` | **Language**: CPP | **Type**: function `unknown()`\n\n```cpp\nFile: main.cpp | L..."}, {"query": "class declaration", "results_count": 3, "preview": "🔍 **Codebase**: test_project\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: 0.099)\n📁 **File**: `main.cpp` | **Language**: CPP | **Type**: class `TestClass`\n\n```cpp\nFile: main.cpp | Langu..."}, {"query": "error handling", "results_count": 3, "preview": "🔍 **Codebase**: test_project\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -0.671)\n📁 **File**: `main.cpp` | **Language**: CPP | **Type**: method `TestClass::unknown()`\n\n```cpp\nFile: mai..."}, {"query": "memory allocation", "results_count": 3, "preview": "🔍 **Codebase**: test_project\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -0.798)\n📁 **File**: `utils.cpp` | **Language**: CPP | **Type**: header\n\n```cpp\nFile: utils.cpp | Language: cpp..."}, {"query": "data structure", "results_count": 3, "preview": "🔍 **Codebase**: test_project\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -0.583)\n📁 **File**: `utils.cpp` | **Language**: CPP | **Type**: header\n\n```cpp\nFile: utils.cpp | Language: cpp..."}, {"query": "pointer arithmetic", "results_count": 3, "preview": "🔍 **Codebase**: test_project\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -0.486)\n📁 **File**: `utils.cpp` | **Language**: CPP | **Type**: header\n\n```cpp\nFile: utils.cpp | Language: cpp..."}, {"query": "struct definition", "results_count": 3, "preview": "🔍 **Codebase**: test_project\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -0.535)\n📁 **File**: `main.cpp` | **Language**: CPP | **Type**: method `TestClass::TestClass()`\n\n```cpp\nFile: m..."}, {"query": "template class", "results_count": 3, "preview": "🔍 **Codebase**: test_project\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -0.119)\n📁 **File**: `main.cpp` | **Language**: CPP | **Type**: class `TestClass`\n\n```cpp\nFile: main.cpp | Lang..."}, {"query": "namespace std", "results_count": 3, "preview": "🔍 **Codebase**: test_project\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: 0.127)\n📁 **File**: `utils.cpp` | **Language**: CPP | **Type**: namespace\n\n```cpp\nFile: utils.cpp | Language: c..."}, {"query": "malloc free", "results_count": 3, "preview": "🔍 **Codebase**: test_project\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -0.818)\n📁 **File**: `utils.cpp` | **Language**: CPP | **Type**: header\n\n```cpp\nFile: utils.cpp | Language: cpp..."}], "success": true}, "ai_test": {"questions_tested": 3, "successful_answers": 3, "results": [{"question": "What encryption functions are available?", "success": true, "response_length": 1846}, {"question": "How is memory management handled?", "success": true, "response_length": 1712}, {"question": "What are the main classes and their purposes?", "success": true, "response_length": 1219}]}, "performance": {"average_time": 0.08348464965820312, "min_time": 0.05856633186340332, "max_time": 0.14221501350402832, "queries_tested": 5}}, {"collection_name": "z80emu", "basic_test": {"collection_name": "z80emu", "queries_tested": 10, "successful_queries": 10, "failed_queries": 0, "query_results": [{"query": "function definition", "results_count": 3, "preview": "🔍 **Codebase**: z80emu\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -0.334)\n📁 **File**: `Essenbee.Z80/Z80.cs` | **Language**: CSHARP | **Type**: function `tFlag(F()`\n\n```csharp\nFile: E..."}, {"query": "class declaration", "results_count": 3, "preview": "🔍 **Codebase**: z80emu\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -0.302)\n📁 **File**: `Essenbee.Z80.Debugger/App.xaml.cs` | **Language**: CSHARP | **Type**: class `p :`\n\n```csharp\nFi..."}, {"query": "error handling", "results_count": 3, "preview": "🔍 **Codebase**: z80emu\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -0.452)\n📁 **File**: `Essenbee.Z80/Z80.Instructions.cs` | **Language**: CSHARP | **Type**: function `P, B()`\n\n```csha..."}, {"query": "memory allocation", "results_count": 3, "preview": "🔍 **Codebase**: z80emu\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -0.495)\n📁 **File**: `Essenbee.Z80.Tests/JumpGroupShould.cs` | **Language**: CSHARP | **Type**: method `mpGroupShould..."}, {"query": "data structure", "results_count": 3, "preview": "🔍 **Codebase**: z80emu\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -0.492)\n📁 **File**: `Essenbee.Spectrum48/Z80Snapshot.cs` | **Language**: CSHARP | **Type**: header\n\n```csharp\nFile: ..."}, {"query": "pointer arithmetic", "results_count": 3, "preview": "🔍 **Codebase**: z80emu\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -0.291)\n📁 **File**: `Essenbee.Z80/Z80.ArithmeticLogic.cs` | **Language**: CSHARP | **Type**: method `0\n ::CAR(b()`\n\n..."}, {"query": "struct definition", "results_count": 3, "preview": "🔍 **Codebase**: z80emu\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -0.660)\n📁 **File**: `Essenbee.Z80.Tests/SixteenBitArithmeticLogicGroupShould.cs` | **Language**: CSHARP | **Type**: ..."}, {"query": "template class", "results_count": 3, "preview": "🔍 **Codebase**: z80emu\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -0.386)\n📁 **File**: `Essenbee.Z80.Tests/Classes/BasicBus.cs` | **Language**: CSHARP | **Type**: using_directive\n\n```..."}, {"query": "namespace std", "results_count": 3, "preview": "🔍 **Codebase**: z80emu\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -0.623)\n📁 **File**: `Essenbee.Z80.Tests/SixteenBitLoadGroupShould.cs` | **Language**: CSHARP | **Type**: namespace\n\n..."}, {"query": "malloc free", "results_count": 3, "preview": "🔍 **Codebase**: z80emu\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -0.593)\n📁 **File**: `Essenbee.Z80.Tests/JumpGroupShould.cs` | **Language**: CSHARP | **Type**: method `mpGroupShould..."}], "success": true}, "ai_test": {"questions_tested": 3, "successful_answers": 3, "results": [{"question": "What encryption functions are available?", "success": true, "response_length": 2335}, {"question": "How is memory management handled?", "success": true, "response_length": 2137}, {"question": "What are the main classes and their purposes?", "success": true, "response_length": 1701}]}, "performance": {"average_time": 0.10532956123352051, "min_time": 0.04912900924682617, "max_time": 0.2594895362854004, "queries_tested": 5}}, {"collection_name": "modbus", "basic_test": {"collection_name": "modbus", "queries_tested": 10, "successful_queries": 10, "failed_queries": 0, "query_results": [{"query": "function definition", "results_count": 3, "fixed": true, "preview": "🔍 **Codebase**: modbus\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -335.758)\n📁 **File**: `smbdata.c` | **Language**: C | **Type**: function `smbdata_diagClearCount()`\n\n```c\nFile: smbd..."}, {"query": "class declaration", "results_count": 3, "preview": "🔍 **Codebase**: modbus\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -403.239)\n📁 **File**: `mbplink.c` | **Language**: C | **Type**: function `mbplink_initConfig()`\n\n```c\nFile: mbplink...."}, {"query": "error handling", "results_count": 3, "preview": "🔍 **Codebase**: modbus\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -347.032)\n📁 **File**: `smbsesn.c` | **Language**: C | **Type**: function `_processReadExceptionStatus()`\n\n```c\nFile:..."}, {"query": "memory allocation", "results_count": 3, "preview": "🔍 **Codebase**: modbus\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -352.886)\n📁 **File**: `mbplink.c` | **Language**: C | **Type**: function `mbplink_getNeededBytes()`\n\n```c\nFile: mbpl..."}, {"query": "data structure", "results_count": 3, "preview": "🔍 **Codebase**: modbus\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -357.404)\n📁 **File**: `mmbmem.c` | **Language**: C | **Type**: struct_specifier\n\n```c\nFile: mmbmem.c | Language: c |..."}, {"query": "pointer arithmetic", "results_count": 3, "preview": "🔍 **Codebase**: modbus\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -382.769)\n📁 **File**: `smbsim.c` | **Language**: C | **Type**: function `smbsim_getPointNumber()`\n\n```c\nFile: smbsim..."}, {"query": "struct definition", "results_count": 3, "preview": "🔍 **Codebase**: modbus\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -286.391)\n📁 **File**: `mbtlink.c` | **Language**: C | **Type**: struct_specifier\n\n```c\nFile: mbtlink.c | Language: c..."}, {"query": "template class", "results_count": 3, "preview": "🔍 **Codebase**: modbus\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -395.829)\n📁 **File**: `mbchnl.h` | **Language**: C | **Type**: struct_specifier\n\n```c\nFile: mbchnl.h | Language: c |..."}, {"query": "namespace std", "results_count": 3, "preview": "🔍 **Codebase**: modbus\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -351.720)\n📁 **File**: `smbmem.h` | **Language**: C | **Type**: struct_specifier\n\n```c\nFile: smbmem.h | Language: c |..."}, {"query": "malloc free", "results_count": 3, "preview": "🔍 **Codebase**: modbus\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -417.186)\n📁 **File**: `mbmem.c` | **Language**: C | **Type**: function `mbmem_free()`\n\n```c\nFile: mbmem.c | Language..."}], "success": true}, "ai_test": {"questions_tested": 3, "successful_answers": 3, "results": [{"question": "What encryption functions are available?", "success": true, "response_length": 1603}, {"question": "How is memory management handled?", "success": true, "response_length": 2608}, {"question": "What are the main classes and their purposes?", "success": true, "response_length": 1762}]}, "performance": {"average_time": 0.07981395721435547, "min_time": 0.04642319679260254, "max_time": 0.15467429161071777, "queries_tested": 5}}, {"collection_name": "networking_project", "basic_test": {"collection_name": "networking_project", "queries_tested": 10, "successful_queries": 10, "failed_queries": 0, "query_results": [{"query": "function definition", "results_count": 3, "preview": "🔍 **Codebase**: networking_project\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -0.248)\n📁 **File**: `socket_manager.cpp` | **Language**: CPP | **Type**: function `unknown()`\n\n```cpp\nFi..."}, {"query": "class declaration", "results_count": 3, "preview": "🔍 **Codebase**: networking_project\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -0.268)\n📁 **File**: `socket_manager.cpp` | **Language**: CPP | **Type**: struct_specifier\n\n```cpp\nFile: ..."}, {"query": "error handling", "results_count": 3, "preview": "🔍 **Codebase**: networking_project\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -0.653)\n📁 **File**: `socket_manager.cpp` | **Language**: CPP | **Type**: class `SocketManager`\n\n```cpp\nF..."}, {"query": "memory allocation", "results_count": 3, "preview": "🔍 **Codebase**: networking_project\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -0.259)\n📁 **File**: `buffer_pool.h` | **Language**: C | **Type**: function `unknown()`\n\n```c\nFile: buffe..."}, {"query": "data structure", "results_count": 3, "preview": "🔍 **Codebase**: networking_project\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -0.671)\n📁 **File**: `socket_manager.cpp` | **Language**: CPP | **Type**: struct_specifier\n\n```cpp\nFile: ..."}, {"query": "pointer arithmetic", "results_count": 3, "preview": "🔍 **Codebase**: networking_project\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -0.511)\n📁 **File**: `buffer_pool.h` | **Language**: C | **Type**: header\n\n```c\nFile: buffer_pool.h | Lan..."}, {"query": "struct definition", "results_count": 3, "preview": "🔍 **Codebase**: networking_project\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -0.200)\n📁 **File**: `socket_manager.cpp` | **Language**: CPP | **Type**: struct_specifier\n\n```cpp\nFile: ..."}, {"query": "template class", "results_count": 3, "preview": "🔍 **Codebase**: networking_project\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -0.497)\n📁 **File**: `socket_manager.cpp` | **Language**: CPP | **Type**: struct_specifier\n\n```cpp\nFile: ..."}, {"query": "namespace std", "results_count": 3, "preview": "🔍 **Codebase**: networking_project\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -0.280)\n📁 **File**: `socket_manager.cpp` | **Language**: CPP | **Type**: function `unknown()`\n\n```cpp\nFi..."}, {"query": "malloc free", "results_count": 3, "preview": "🔍 **Codebase**: networking_project\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -0.484)\n📁 **File**: `socket_manager.cpp` | **Language**: CPP | **Type**: function `unknown()`\n\n```cpp\nFi..."}], "success": true}, "ai_test": {"questions_tested": 3, "successful_answers": 3, "results": [{"question": "What encryption functions are available?", "success": true, "response_length": 1322}, {"question": "How is memory management handled?", "success": true, "response_length": 1698}, {"question": "What are the main classes and their purposes?", "success": true, "response_length": 1835}]}, "performance": {"average_time": 0.09290037155151368, "min_time": 0.05471611022949219, "max_time": 0.15874671936035156, "queries_tested": 5}}]}