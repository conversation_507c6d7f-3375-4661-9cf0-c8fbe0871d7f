#!/usr/bin/env python3
"""
Collection Testing Script for Multi-Language RAG System

This script provides comprehensive testing and validation for ChromaDB collections
containing code chunks from C/C++, Python, and C# codebases.

Updated for home-ai-server deployment on port 5002.

This version runs automatically without command line arguments and performs:
- Discovery of all available collections
- Basic collection query testing with default queries
- AI question answering testing
- Performance benchmarking
- Automatic export of results to timestamped JSON file

Usage:
    python test_collections.py
"""

import sys
import os
import requests
from typing import List, Dict, Optional
import json
from datetime import datetime

# Configuration for home-ai-server
RAG_SERVER_HOST = os.getenv("RAG_SERVER_HOST", "home-ai-server")
RAG_SERVER_PORT = os.getenv("RAG_SERVER_PORT", "5002")
RAG_SERVER_URL = f"http://{RAG_SERVER_HOST}:{RAG_SERVER_PORT}"

class RAGServerTester:
    def __init__(self, server_url: str = RAG_SERVER_URL):
        """Initialize the RAG server tester"""
        self.server_url = server_url
        print(f"🔗 Connecting to RAG server at: {server_url}")
        
        # Test connection
        try:
            response = requests.get(f"{server_url}/health", timeout=10)
            if response.status_code == 200:
                print("✅ RAG server is responding")
                health = response.json()
                print(f"   Server status: {health.get('overall_status', 'unknown')}")
                print(f"   Embedding provider: {health.get('embedding_provider', 'unknown')}")
                print(f"   Collections: {health.get('chromadb_collections', 0)}")
            else:
                print(f"⚠️ RAG server responded with status {response.status_code}")
        except Exception as e:
            print(f"❌ Failed to connect to RAG server: {e}")
            print(f"   Make sure the server is running at {server_url}")
            sys.exit(1)
    
    def list_collections(self) -> List[Dict]:
        """List all available collections via API"""
        try:
            response = requests.post(f"{self.server_url}/tools/list_codebases", timeout=30)
            if response.status_code == 200:
                result = response.json()
                # Parse the result text to extract codebase info
                # This is a simplified parser - in real use you'd want more robust parsing
                result_text = result.get('result', '')
                
                # For now, just return a simple success indicator
                if 'No codebases found' in result_text:
                    return []
                else:
                    # Extract codebase names from the result text
                    # This is a basic implementation
                    lines = result_text.split('\n')
                    codebases = []
                    for line in lines:
                        if line.strip().startswith('**') and ('✅' in line or '⚠️' in line or '📦' in line):
                            # Extract name from markdown format
                            parts = line.split('**')
                            if len(parts) >= 2:
                                name_part = parts[1].strip()
                                # Remove emoji
                                name = ''.join(c for c in name_part if c.isalnum() or c in ['_', '-']).strip()
                                if name:
                                    codebases.append({'name': name, 'status': 'detected'})
                    return codebases
            else:
                print(f"❌ Error listing codebases: HTTP {response.status_code}")
                return []
        except Exception as e:
            print(f"❌ Error listing codebases: {e}")
            return []
    
    def get_collection_info(self, collection_name: str) -> Dict:
        """Get detailed information about a collection via API"""
        try:
            payload = {"codebase_name": collection_name}
            response = requests.post(f"{self.server_url}/tools/get_code_stats", 
                                   json=payload, timeout=30)
            if response.status_code == 200:
                result = response.json()
                result_text = result.get('result', '')
                
                # Parse the stats from the result text
                info = {
                    'name': collection_name,
                    'total_documents': 0,
                    'languages': {},
                    'types': {},
                    'status': 'unknown'
                }
                
                # Basic parsing of the stats text
                lines = result_text.split('\n')
                for line in lines:
                    if 'Total code chunks:' in line:
                        try:
                            info['total_documents'] = int(line.split(':')[1].strip().replace(',', ''))
                        except:
                            pass
                
                return info
            else:
                return {'error': f'HTTP {response.status_code}'}
        except Exception as e:
            return {'error': str(e)}
    
    def test_collection(self, collection_name: str, custom_queries: Optional[List[str]] = None, 
                       n_results: int = 3, verbose: bool = True) -> Dict:
        """Test a collection with various queries via API"""
        
        print(f"\n{'='*60}")
        print(f"🧪 TESTING COLLECTION VIA RAG SERVER: {collection_name}")
        print(f"{'='*60}")
        
        # First, select the codebase
        try:
            select_payload = {"codebase_name": collection_name}
            response = requests.post(f"{self.server_url}/tools/select_codebase", 
                                   json=select_payload, timeout=30)
            if response.status_code != 200:
                return {'success': False, 'error': f'Failed to select codebase: HTTP {response.status_code}'}
            
            result = response.json()
            if 'Failed to select' in result.get('result', ''):
                return {'success': False, 'error': 'Codebase selection failed'}
            
            print(f"✅ Selected codebase: {collection_name}")
            
        except Exception as e:
            return {'success': False, 'error': f'Failed to select codebase: {e}'}
        
        # Default test queries for different languages and patterns
        default_queries = [
            # Generic programming concepts
            "function definition",
            "class declaration", 
            "error handling",
            "memory allocation",
            "data structure",
            
            # C/C++ specific
            "pointer arithmetic",
            "struct definition",
            "template class",
            "namespace std",
            "malloc free",
            
            # Python specific  
            "import statement",
            "def __init__",
            "try except",
            "list comprehension",
            
            # C# specific
            "using System",
            "public class",
            "async await",
            "LINQ query",
            
            # Common patterns
            "encryption",
            "database connection",
            "file operations",
            "network communication"
        ]
        
        # Use custom queries if provided, otherwise use defaults
        test_queries = custom_queries if custom_queries else default_queries[:10]
        
        # Initialize results summary with explicit type annotation to fix IDE type checking
        results_summary: Dict = {
            'collection_name': collection_name,
            'queries_tested': len(test_queries),
            'successful_queries': 0,
            'failed_queries': 0,
            'query_results': []  # Explicitly initialize as empty list
        }

        # Defensive check to ensure query_results is a list
        if not isinstance(results_summary['query_results'], list):
            results_summary['query_results'] = []
        
        for i, query in enumerate(test_queries, 1):
            print(f"\n🔍 Query {i}/{len(test_queries)}: '{query}'")
            
            try:
                search_payload = {
                    "query": query,
                    "codebase_name": collection_name,
                    "n_results": n_results
                }
                response = requests.post(f"{self.server_url}/tools/search_code", 
                                       json=search_payload, timeout=30)
                
                if response.status_code == 200:
                    result = response.json()
                    result_text = result.get('result', '')
                    
                    if 'No relevant code found' in result_text:
                        print("   ❌ No results found")
                        results_summary['failed_queries'] += 1
                        # Ensure query_results is a list before appending
                        if not isinstance(results_summary['query_results'], list):
                            results_summary['query_results'] = []
                        results_summary['query_results'].append({
                            'query': query,
                            'results_count': 0,
                            'results': []
                        })
                    else:
                        # Count results by looking for "Result" markers
                        result_count = result_text.count('**Result')
                        print(f"   ✅ Found {result_count} results")
                        results_summary['successful_queries'] += 1
                        
                        if verbose:
                            # Show first few lines of results
                            lines = result_text.split('\n')[:10]
                            for line in lines:
                                if line.strip() and not line.startswith('🔍'):
                                    print(f"      {line}")

                        # Ensure query_results is a list before appending
                        if not isinstance(results_summary['query_results'], list):
                            results_summary['query_results'] = []
                        results_summary['query_results'].append({
                            'query': query,
                            'results_count': result_count,
                            'preview': result_text[:200] + "..." if len(result_text) > 200 else result_text
                        })
                else:
                    print(f"   ❌ Query failed: HTTP {response.status_code}")
                    results_summary['failed_queries'] += 1
                    
            except Exception as e:
                print(f"   ❌ Query failed: {e}")
                results_summary['failed_queries'] += 1
        
        print("\n📈 SUMMARY:")
        print(f"   ✅ Successful queries: {results_summary['successful_queries']}/{len(test_queries)}")
        print(f"   ❌ Failed queries: {results_summary['failed_queries']}")
        
        results_summary['success'] = True
        return results_summary
    
    def test_question_answering(self, collection_name: str, questions: Optional[List[str]] = None) -> Dict:
        """Test the AI question answering capability"""
        print(f"\n🤖 TESTING AI QUESTION ANSWERING: {collection_name}")
        
        default_questions = [
            "What encryption functions are available?",
            "How is memory management handled?",
            "What are the main classes and their purposes?",
            "Show me error handling patterns",
            "What database operations are implemented?"
        ]
        
        test_questions = questions if questions else default_questions[:3]
        
        results = []
        
        for i, question in enumerate(test_questions, 1):
            print(f"\n❓ Question {i}/{len(test_questions)}: {question}")
            
            try:
                ask_payload = {
                    "question": question,
                    "codebase_name": collection_name,
                    "n_results": 5
                }
                response = requests.post(f"{self.server_url}/tools/ask_about_code", 
                                       json=ask_payload, timeout=60)
                
                if response.status_code == 200:
                    result = response.json()
                    result_text = result.get('result', '')
                    
                    print("   ✅ Got AI response")
                    # Show first few lines of the response
                    lines = result_text.split('\n')[:5]
                    for line in lines:
                        if line.strip():
                            print(f"      {line[:100]}{'...' if len(line) > 100 else ''}")
                    
                    results.append({
                        'question': question,
                        'success': True,
                        'response_length': len(result_text)
                    })
                else:
                    print(f"   ❌ Failed: HTTP {response.status_code}")
                    results.append({
                        'question': question,
                        'success': False,
                        'error': f"HTTP {response.status_code}"
                    })
                    
            except Exception as e:
                print(f"   ❌ Failed: {e}")
                results.append({
                    'question': question,
                    'success': False,
                    'error': str(e)
                })
        
        successful = sum(1 for r in results if r['success'])
        print(f"\n🎯 AI Question Results: {successful}/{len(test_questions)} successful")
        
        return {
            'questions_tested': len(test_questions),
            'successful_answers': successful,
            'results': results
        }
    
    def benchmark_performance(self, collection_name: str, num_queries: int = 5) -> Dict:
        """Benchmark search performance via API"""
        print(f"\n⚡ PERFORMANCE BENCHMARK VIA API: {collection_name}")
        
        test_queries = [
            "function", "class", "error", "data", "file",
            "memory", "network", "database", "encrypt", "parse"
        ][:num_queries]
        
        import time
        times = []
        
        for query in test_queries:
            try:
                start_time = time.time()
                
                search_payload = {
                    "query": query,
                    "codebase_name": collection_name,
                    "n_results": 5
                }
                response = requests.post(f"{self.server_url}/tools/search_code", 
                                       json=search_payload, timeout=30)
                
                end_time = time.time()
                query_time = end_time - start_time
                times.append(query_time)
                
                result_count = 0
                if response.status_code == 200:
                    result = response.json()
                    result_text = result.get('result', '')
                    result_count = result_text.count('**Result')
                
                print(f"   🔍 '{query}': {query_time:.3f}s ({result_count} results)")
                
            except Exception as e:
                print(f"   ❌ '{query}': Failed - {e}")
        
        if times:
            avg_time = sum(times) / len(times)
            min_time = min(times)
            max_time = max(times)
            
            print("\n📊 PERFORMANCE SUMMARY:")
            print(f"   ⚡ Average query time: {avg_time:.3f}s")
            print(f"   🚀 Fastest query: {min_time:.3f}s")
            print(f"   🐌 Slowest query: {max_time:.3f}s")
            
            return {
                'average_time': avg_time,
                'min_time': min_time,
                'max_time': max_time,
                'queries_tested': len(times)
            }
        else:
            return {'error': 'No successful queries'}
    
    def export_test_report(self, results: List[Dict], output_file: str):
        """Export test results to JSON file"""
        try:
            report = {
                'timestamp': datetime.now().isoformat(),
                'rag_server_url': self.server_url,
                'test_results': results
            }
            
            with open(output_file, 'w') as f:
                json.dump(report, f, indent=2, default=str)
            
            print(f"📄 Test report exported to: {output_file}")
            
        except Exception as e:
            print(f"❌ Error exporting report: {e}")

def main():
    """
    Run comprehensive testing of all RAG server collections.
    This version runs all tests automatically without command line arguments.
    """
    print("🚀 Starting comprehensive RAG server collection testing...")
    print("=" * 80)

    # Initialize tester with default server URL
    tester = RAGServerTester(server_url=RAG_SERVER_URL)

    # First, list all available collections
    print("\n📚 DISCOVERING COLLECTIONS...")
    collections = tester.list_collections()

    if not collections:
        print("❌ No collections found on the server")
        return

    print(f"\n📚 Available Collections ({len(collections)}):")
    for i, codebase in enumerate(collections, 1):
        print(f"   {i}. {codebase['name']} ({codebase['status']})")
        # Get detailed info
        info = tester.get_collection_info(codebase['name'])
        if 'error' not in info:
            print(f"      📊 Documents: {info.get('total_documents', 'unknown')}")
        else:
            print(f"      ❌ Error getting info: {info['error']}")

    # Test all available collections
    collections_to_test = [cb['name'] for cb in collections]

    if not collections_to_test:
        print("❌ No collections found to test")
        return

    print(f"\n🧪 Will test {len(collections_to_test)} collection(s) with comprehensive testing:")
    print("   • Basic collection queries")
    print("   • AI question answering")
    print("   • Performance benchmarking")
    print("   • Results export to JSON")

    # Run comprehensive tests
    all_results = []

    for i, collection_name in enumerate(collections_to_test, 1):
        print(f"\n{'='*80}")
        print(f"🧪 TESTING {i}/{len(collections_to_test)}: {collection_name}")
        print(f"{'='*80}")

        # Basic collection test with default queries
        print("\n🔍 Running basic collection queries...")
        test_result = tester.test_collection(
            collection_name,
            custom_queries=None,  # Use default queries
            n_results=3,
            verbose=True
        )

        collection_results = {
            'collection_name': collection_name,
            'basic_test': test_result
        }

        # AI question answering test
        print("\n🤖 Running AI question answering test...")
        ai_result = tester.test_question_answering(collection_name, questions=None)  # Use default questions
        collection_results['ai_test'] = ai_result

        # Performance benchmark
        print("\n⚡ Running performance benchmark...")
        benchmark_result = tester.benchmark_performance(collection_name)
        collection_results['performance'] = benchmark_result

        all_results.append(collection_results)

        # Brief summary for this collection
        basic_success = test_result.get('success', False) if isinstance(test_result, dict) else False
        ai_success = ai_result.get('success', False) if isinstance(ai_result, dict) else False
        perf_success = benchmark_result.get('success', False) if isinstance(benchmark_result, dict) else False

        print(f"\n📊 Collection {collection_name} Summary:")
        print(f"   Basic Test: {'✅ PASS' if basic_success else '❌ FAIL'}")
        print(f"   AI Test: {'✅ PASS' if ai_success else '❌ FAIL'}")
        print(f"   Performance: {'✅ PASS' if perf_success else '❌ FAIL'}")

    # Export results with timestamp
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_file = f"rag_test_results_{timestamp}.json"

    print("\n💾 Exporting comprehensive test results...")
    tester.export_test_report(all_results, output_file)

    # Final summary
    print("\n🎉 COMPREHENSIVE TESTING COMPLETE!")
    print(f"   📊 Collections tested: {len(collections_to_test)}")
    print(f"   📄 Results exported to: {output_file}")
    print(f"   🕒 Completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    # Summary statistics
    total_basic_pass = sum(1 for r in all_results if r.get('basic_test', {}).get('success', False))
    total_ai_pass = sum(1 for r in all_results if r.get('ai_test', {}).get('success', False))
    total_perf_pass = sum(1 for r in all_results if r.get('performance', {}).get('success', False))

    print("\n📈 OVERALL STATISTICS:")
    print(f"   Basic Tests: {total_basic_pass}/{len(collections_to_test)} passed")
    print(f"   AI Tests: {total_ai_pass}/{len(collections_to_test)} passed")
    print(f"   Performance Tests: {total_perf_pass}/{len(collections_to_test)} passed")

if __name__ == "__main__":
    main()