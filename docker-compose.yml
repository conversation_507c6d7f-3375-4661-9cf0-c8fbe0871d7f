version: '3.8'

services:
  # LLM and Chat Interface
  ollama:
    container_name: ollama
    networks:
      - ollama-network
    image: ollama/ollama:latest
    restart: unless-stopped
    environment:
      - OLLAMA_HOST=0.0.0.0:11434
      - OLLAMA_GPU_LAYERS=60
      - OLLAMA_KEEP_ALIVE=-1
      - OLLAMA_CUDA_MAX_MEMORY=23000000000  # 23GB VRAM limit
      - NVIDIA_VISIBLE_DEVICES=all
    runtime: nvidia
    ports:
      - "11434:11434"
    volumes:
      - ollama_data:/root/.ollama
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              capabilities: [gpu]
              device_ids: ['0']

  open-webui:
    container_name: open-webui
    networks:
      - ollama-network
    image: ghcr.io/open-webui/open-webui:main
    restart: unless-stopped
    ports:
      - "8080:8080"
    volumes:
      - open-webui_data:/app/backend/data
      - ./webui_tools:/app/backend/data/tools # This is where OpenWebUI looks for tool JSONs
    environment:
      - OLLAMA_API_BASE_URL=http://ollama:11434/api
      - WEBUI_AUTH=false
      - ENABLE_IMAGE_GENERATION=true
      - IMAGE_GENERATION_API_URL=http://automatic1111:7860/sdapi/v1/txt2img  # Link to Stable Diffusion
      - ENABLE_FUNCTIONS=true  # Add this line
      - WEBUI_LOG_LEVEL=DEBUG
      - OPENWEATHER_API_KEY=********************************
    depends_on:
      - ollama
      - stable-diffusion-webui
      - openwebui-tool-server # Add dependency

  # OpenWebUI Tool Server >>>
  openwebui-tool-server:
    container_name: openwebui-tool-server
    build:
      context: ./openwebui_tool_server # Path to the directory containing its Dockerfile
    restart: unless-stopped
    networks:
      - ollama-network
    ports:
      - "5001:5001" # Expose to host (optional, for direct testing)
    environment:
      - LOG_LEVEL=info # Example environment variable for your tool server if needed
      - OPENWEATHER_API_KEY=********************************
    # No volumes needed for this simple server unless it needs persistent data

  # OpenWebUI RAG Tool Server (Port 5002) >>>
  openwebui-rag-server:
    container_name: openwebui-rag-server
    build:
      context: ./openwebui_rag_server # Separate directory for RAG microservice
    restart: unless-stopped
    networks:
      - ollama-network
    ports:
      - "5002:5002" # RAG tools server
    environment:
      - LOG_LEVEL=info
      - OLLAMA_HOST=http://ollama:11434  # Connect to existing Ollama
      - CHROMA_DB_PATH=/app/chroma_db
      - COLLECTION_NAME=utils
      - USE_OLLAMA_EMBEDDINGS=true
      - DEBUG=true
      - PYTHONUNBUFFERED=1  # Ensure immediate output to logs
      - PYTHONIOENCODING=utf-8
    volumes:
      - chroma_db:/app/chroma_db # Persist ChromaDB data
      - ./source_code:/app/source_code:ro # Mount source code read-only
    depends_on:
      - ollama

  # OpenHands AI Agent
  openhands:
    container_name: openhands
    networks:
      - ollama-network
    image: docker.all-hands.dev/all-hands-ai/openhands:0.40
    restart: unless-stopped
    privileged: true
    ports:
      - "3000:3000"
    environment:
      - SANDBOX_RUNTIME_CONTAINER_IMAGE=docker.all-hands.dev/all-hands-ai/runtime:0.40-nikolaik
      - LOG_ALL_EVENTS=true
      # Connect to Ollama - using ollama service name for internal network
      - LLM_OLLAMA_BASE_URL=http://ollama:11434
    extra_hosts:
      - "host.docker.internal:host-gateway"
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
      # Optional: mount workspace directory
      - ${WORKSPACE_BASE:-./workspace}:/opt/workspace_base
    depends_on:
      - ollama
    stdin_open: true
    tty: true

  # Stable Diffusion WebUI
  stable-diffusion-webui:
    container_name: automatic1111
    networks:
      - ollama-network
    image: emsi/stable-diffusion-webui
    restart: unless-stopped
    runtime: nvidia
    ports:
      - "7860:7860"
    environment:
      - NVIDIA_VISIBLE_DEVICES=0
    volumes:
      - sd_models:/stable-diffusion-webui/models
      - sd_outputs:/stable-diffusion-webui/outputs
    command: python3 launch.py --listen --medvram --xformers --api  # <-- Added --api flag
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              capabilities: [gpu]
              device_ids: ['0']

  # Container Management GUI
  portainer:
    container_name: portainer
    image: portainer/portainer-ce:latest
    restart: unless-stopped
    networks:
      - ollama-network
    ports:
      - "9000:9000"
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
      - portainer_data:/data
    environment:
      - TZ=UTC

  # Vector Database for RAG
  qdrant:
    container_name: qdrant
    networks:
      - ollama-network
    image: qdrant/qdrant:latest
    restart: unless-stopped
    ports:
      - "6333:6333"
      - "6334:6334"
    volumes:
      - qdrant_data:/qdrant/storage
    environment:
      - QDRANT_ALLOW_RECOVERY=true
    command: ["./qdrant", "--memory-size", "8GB"]  # Adjusted memory limit

  # Speech-to-Text (Whisper)
  whisper:
    container_name: whisper
    networks:
      - ollama-network
    image: rhasspy/wyoming-whisper:latest
    restart: unless-stopped
    ports:
      - "10300:10300"
    volumes:
      - whisper_data:/data
    environment:
      - WHISPER_MODEL=base
      - COMPUTE_TYPE=int8
    command: --uri 'tcp://0.0.0.0:10300' --data-dir /data --model base --compute-type int8
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              capabilities: [gpu]
              device_ids: ['0']

  # Text-to-Speech
  tts:
    container_name: tts
    networks:
      - ollama-network
    image: synesthesiam/opentts:latest
    restart: unless-stopped
    ports:
      - "5500:5500"
    volumes:
      - tts_data:/app/data
    environment:
      - TZ=UTC

  # Document Processing & OCR
  paperless:
    container_name: paperless
    networks:
      - ollama-network
    image: ghcr.io/paperless-ngx/paperless-ngx:latest
    restart: unless-stopped
    ports:
      - "0.0.0.0:8000:8000"
    volumes:
      - paperless_data:/usr/src/paperless/data
      - paperless_media:/usr/src/paperless/media
      - paperless_export:/usr/src/paperless/export
      - paperless_consume:/usr/src/paperless/consume
    environment:
      - PAPERLESS_REDIS=redis://paperless-redis:6379
      - PAPERLESS_URL=http://************:8000
      - PAPERLESS_OCR_LANGUAGE=eng
      - PAPERLESS_OCR_MODE=skip
      - PAPERLESS_TIME_ZONE=UTC
      - PAPERLESS_SECRET_KEY=changemeplease
      - PAPERLESS_ADMIN_USER=admin
      - PAPERLESS_ADMIN_PASSWORD=adminpassword
      - PAPERLESS_WORKER_TIMEOUT=3600
      - PAPERLESS_WEBSERVER_WORKERS=2
      - PAPERLESS_ALLOWED_HOSTS=*

  redis:
    container_name: paperless-redis
    image: redis:7
    restart: unless-stopped
    networks:
      - ollama-network
    volumes:
      - paperless_redis:/data

  # Media Management with Image Recognition
  komga:
    container_name: komga
    networks:
      - ollama-network
    image: gotson/komga:latest
    restart: unless-stopped
    ports:
      - "8001:25600"
    volumes:
      - komga_config:/config
      - komga_media:/data
    environment:
      - TZ=UTC
      - KOMGA_CONFIGDIR=/config
      - KOMGA_LIBRARIESDIR=/data
      - KOMGA_MEMORY_LIMIT=2048m

  # Python jupyter notebooks etc.
  jupyterlab:
    container_name: jupyterlab
    image: jupyter/datascience-notebook:latest
    restart: unless-stopped
    networks:
      - ollama-network
    ports:
      - "8888:8888"
    environment:
      - JUPYTER_TOKEN=fvaneijk
    volumes:
      - ./notebooks:/home/<USER>/work


networks:
  ollama-network:
    driver: bridge

volumes:
  ollama_data:
  open-webui_data:
  chroma_db:
  sd_models:
  sd_outputs:
  qdrant_data:
  whisper_data:
  tts_data:
  paperless_data:
  paperless_media:
  paperless_export:
  paperless_consume:
  paperless_redis:
  komga_config:
  komga_media:
  openhands_state:
  portainer_data:
