{"timestamp": "2025-06-25T22:04:41.009081", "rag_server_url": "http://home-ai-server:5002", "test_results": [{"collection_name": "utils", "basic_test": {"collection_name": "utils", "queries_tested": 10, "successful_queries": 10, "failed_queries": 0, "query_results": [{"query": "function definition", "results_count": 0, "preview": "❌ Error searching code: Embedding dimension 384 does not match collection dimensionality 768"}, {"query": "class declaration", "results_count": 0, "preview": "❌ Error searching code: Embedding dimension 384 does not match collection dimensionality 768"}, {"query": "error handling", "results_count": 0, "preview": "❌ Error searching code: Embedding dimension 384 does not match collection dimensionality 768"}, {"query": "memory allocation", "results_count": 0, "preview": "❌ Error searching code: Embedding dimension 384 does not match collection dimensionality 768"}, {"query": "data structure", "results_count": 0, "preview": "❌ Error searching code: Embedding dimension 384 does not match collection dimensionality 768"}, {"query": "pointer arithmetic", "results_count": 0, "preview": "❌ Error searching code: Embedding dimension 384 does not match collection dimensionality 768"}, {"query": "struct definition", "results_count": 0, "preview": "❌ Error searching code: Embedding dimension 384 does not match collection dimensionality 768"}, {"query": "template class", "results_count": 0, "preview": "❌ Error searching code: Embedding dimension 384 does not match collection dimensionality 768"}, {"query": "namespace std", "results_count": 0, "preview": "❌ Error searching code: Embedding dimension 384 does not match collection dimensionality 768"}, {"query": "malloc free", "results_count": 0, "preview": "❌ Error searching code: Embedding dimension 384 does not match collection dimensionality 768"}], "success": true}, "ai_test": {"questions_tested": 3, "successful_answers": 3, "results": [{"question": "What encryption functions are available?", "success": true, "response_length": 95}, {"question": "How is memory management handled?", "success": true, "response_length": 95}, {"question": "What are the main classes and their purposes?", "success": true, "response_length": 95}]}, "performance": {"average_time": 0.0924311637878418, "min_time": 0.07504796981811523, "max_time": 0.1038675308227539, "queries_tested": 5}}, {"collection_name": "test_project", "basic_test": {"collection_name": "test_project", "queries_tested": 10, "successful_queries": 10, "failed_queries": 0, "query_results": [{"query": "function definition", "results_count": 3, "preview": "🔍 **Codebase**: test_project\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -0.216)\n📁 **File**: `main.cpp` | **Language**: CPP | **Type**: function `unknown()`\n\n```cpp\nFile: main.cpp | L..."}, {"query": "class declaration", "results_count": 3, "preview": "🔍 **Codebase**: test_project\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: 0.099)\n📁 **File**: `main.cpp` | **Language**: CPP | **Type**: class `TestClass`\n\n```cpp\nFile: main.cpp | Langu..."}, {"query": "error handling", "results_count": 3, "preview": "🔍 **Codebase**: test_project\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -0.671)\n📁 **File**: `main.cpp` | **Language**: CPP | **Type**: method `TestClass::unknown()`\n\n```cpp\nFile: mai..."}, {"query": "memory allocation", "results_count": 3, "preview": "🔍 **Codebase**: test_project\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -0.798)\n📁 **File**: `utils.cpp` | **Language**: CPP | **Type**: header\n\n```cpp\nFile: utils.cpp | Language: cpp..."}, {"query": "data structure", "results_count": 3, "preview": "🔍 **Codebase**: test_project\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -0.583)\n📁 **File**: `utils.cpp` | **Language**: CPP | **Type**: header\n\n```cpp\nFile: utils.cpp | Language: cpp..."}, {"query": "pointer arithmetic", "results_count": 3, "preview": "🔍 **Codebase**: test_project\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -0.486)\n📁 **File**: `utils.cpp` | **Language**: CPP | **Type**: header\n\n```cpp\nFile: utils.cpp | Language: cpp..."}, {"query": "struct definition", "results_count": 3, "preview": "🔍 **Codebase**: test_project\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -0.535)\n📁 **File**: `main.cpp` | **Language**: CPP | **Type**: method `TestClass::TestClass()`\n\n```cpp\nFile: m..."}, {"query": "template class", "results_count": 3, "preview": "🔍 **Codebase**: test_project\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -0.119)\n📁 **File**: `main.cpp` | **Language**: CPP | **Type**: class `TestClass`\n\n```cpp\nFile: main.cpp | Lang..."}, {"query": "namespace std", "results_count": 3, "preview": "🔍 **Codebase**: test_project\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: 0.127)\n📁 **File**: `utils.cpp` | **Language**: CPP | **Type**: namespace\n\n```cpp\nFile: utils.cpp | Language: c..."}, {"query": "malloc free", "results_count": 3, "preview": "🔍 **Codebase**: test_project\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -0.818)\n📁 **File**: `utils.cpp` | **Language**: CPP | **Type**: header\n\n```cpp\nFile: utils.cpp | Language: cpp..."}], "success": true}, "ai_test": {"questions_tested": 3, "successful_answers": 3, "results": [{"question": "What encryption functions are available?", "success": true, "response_length": 1624}, {"question": "How is memory management handled?", "success": true, "response_length": 1851}, {"question": "What are the main classes and their purposes?", "success": true, "response_length": 1226}]}, "performance": {"average_time": 0.08066644668579101, "min_time": 0.04803824424743652, "max_time": 0.12929391860961914, "queries_tested": 5}}, {"collection_name": "z80emu", "basic_test": {"collection_name": "z80emu", "queries_tested": 10, "successful_queries": 10, "failed_queries": 0, "query_results": [{"query": "function definition", "results_count": 3, "preview": "🔍 **Codebase**: z80emu\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -0.334)\n📁 **File**: `Essenbee.Z80/Z80.cs` | **Language**: CSHARP | **Type**: function `tFlag(F()`\n\n```csharp\nFile: E..."}, {"query": "class declaration", "results_count": 3, "preview": "🔍 **Codebase**: z80emu\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -0.302)\n📁 **File**: `Essenbee.Z80.Debugger/App.xaml.cs` | **Language**: CSHARP | **Type**: class `p :`\n\n```csharp\nFi..."}, {"query": "error handling", "results_count": 3, "preview": "🔍 **Codebase**: z80emu\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -0.452)\n📁 **File**: `Essenbee.Z80/Z80.Instructions.cs` | **Language**: CSHARP | **Type**: function `P, B()`\n\n```csha..."}, {"query": "memory allocation", "results_count": 3, "preview": "🔍 **Codebase**: z80emu\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -0.495)\n📁 **File**: `Essenbee.Z80.Tests/JumpGroupShould.cs` | **Language**: CSHARP | **Type**: method `mpGroupShould..."}, {"query": "data structure", "results_count": 3, "preview": "🔍 **Codebase**: z80emu\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -0.492)\n📁 **File**: `Essenbee.Spectrum48/Z80Snapshot.cs` | **Language**: CSHARP | **Type**: header\n\n```csharp\nFile: ..."}, {"query": "pointer arithmetic", "results_count": 3, "preview": "🔍 **Codebase**: z80emu\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -0.291)\n📁 **File**: `Essenbee.Z80/Z80.ArithmeticLogic.cs` | **Language**: CSHARP | **Type**: method `0\n ::CAR(b()`\n\n..."}, {"query": "struct definition", "results_count": 3, "preview": "🔍 **Codebase**: z80emu\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -0.660)\n📁 **File**: `Essenbee.Z80.Tests/SixteenBitArithmeticLogicGroupShould.cs` | **Language**: CSHARP | **Type**: ..."}, {"query": "template class", "results_count": 3, "preview": "🔍 **Codebase**: z80emu\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -0.386)\n📁 **File**: `Essenbee.Z80.Tests/Classes/BasicBus.cs` | **Language**: CSHARP | **Type**: using_directive\n\n```..."}, {"query": "namespace std", "results_count": 3, "preview": "🔍 **Codebase**: z80emu\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -0.623)\n📁 **File**: `Essenbee.Z80.Tests/SixteenBitLoadGroupShould.cs` | **Language**: CSHARP | **Type**: namespace\n\n..."}, {"query": "malloc free", "results_count": 3, "preview": "🔍 **Codebase**: z80emu\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -0.593)\n📁 **File**: `Essenbee.Z80.Tests/JumpGroupShould.cs` | **Language**: CSHARP | **Type**: method `mpGroupShould..."}], "success": true}, "ai_test": {"questions_tested": 3, "successful_answers": 3, "results": [{"question": "What encryption functions are available?", "success": true, "response_length": 2274}, {"question": "How is memory management handled?", "success": true, "response_length": 1749}, {"question": "What are the main classes and their purposes?", "success": true, "response_length": 2242}]}, "performance": {"average_time": 0.08642992973327637, "min_time": 0.04402875900268555, "max_time": 0.20760822296142578, "queries_tested": 5}}, {"collection_name": "modbus", "basic_test": {"collection_name": "modbus", "queries_tested": 10, "successful_queries": 10, "failed_queries": 0, "query_results": [{"query": "function definition", "results_count": 0, "preview": "❌ Error searching code: Embedding dimension 384 does not match collection dimensionality 768"}, {"query": "class declaration", "results_count": 0, "preview": "❌ Error searching code: Embedding dimension 384 does not match collection dimensionality 768"}, {"query": "error handling", "results_count": 0, "preview": "❌ Error searching code: Embedding dimension 384 does not match collection dimensionality 768"}, {"query": "memory allocation", "results_count": 0, "preview": "❌ Error searching code: Embedding dimension 384 does not match collection dimensionality 768"}, {"query": "data structure", "results_count": 0, "preview": "❌ Error searching code: Embedding dimension 384 does not match collection dimensionality 768"}, {"query": "pointer arithmetic", "results_count": 0, "preview": "❌ Error searching code: Embedding dimension 384 does not match collection dimensionality 768"}, {"query": "struct definition", "results_count": 0, "preview": "❌ Error searching code: Embedding dimension 384 does not match collection dimensionality 768"}, {"query": "template class", "results_count": 0, "preview": "❌ Error searching code: Embedding dimension 384 does not match collection dimensionality 768"}, {"query": "namespace std", "results_count": 0, "preview": "❌ Error searching code: Embedding dimension 384 does not match collection dimensionality 768"}, {"query": "malloc free", "results_count": 0, "preview": "❌ Error searching code: Embedding dimension 384 does not match collection dimensionality 768"}], "success": true}, "ai_test": {"questions_tested": 3, "successful_answers": 3, "results": [{"question": "What encryption functions are available?", "success": true, "response_length": 95}, {"question": "How is memory management handled?", "success": true, "response_length": 95}, {"question": "What are the main classes and their purposes?", "success": true, "response_length": 95}]}, "performance": {"average_time": 0.04984292984008789, "min_time": 0.035010337829589844, "max_time": 0.05760550498962402, "queries_tested": 5}}, {"collection_name": "networking_project", "basic_test": {"collection_name": "networking_project", "queries_tested": 10, "successful_queries": 10, "failed_queries": 0, "query_results": [{"query": "function definition", "results_count": 3, "preview": "🔍 **Codebase**: networking_project\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -0.248)\n📁 **File**: `socket_manager.cpp` | **Language**: CPP | **Type**: function `unknown()`\n\n```cpp\nFi..."}, {"query": "class declaration", "results_count": 3, "preview": "🔍 **Codebase**: networking_project\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -0.268)\n📁 **File**: `socket_manager.cpp` | **Language**: CPP | **Type**: struct_specifier\n\n```cpp\nFile: ..."}, {"query": "error handling", "results_count": 3, "preview": "🔍 **Codebase**: networking_project\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -0.653)\n📁 **File**: `socket_manager.cpp` | **Language**: CPP | **Type**: class `SocketManager`\n\n```cpp\nF..."}, {"query": "memory allocation", "results_count": 3, "preview": "🔍 **Codebase**: networking_project\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -0.259)\n📁 **File**: `buffer_pool.h` | **Language**: C | **Type**: function `unknown()`\n\n```c\nFile: buffe..."}, {"query": "data structure", "results_count": 3, "preview": "🔍 **Codebase**: networking_project\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -0.671)\n📁 **File**: `socket_manager.cpp` | **Language**: CPP | **Type**: struct_specifier\n\n```cpp\nFile: ..."}, {"query": "pointer arithmetic", "results_count": 3, "preview": "🔍 **Codebase**: networking_project\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -0.511)\n📁 **File**: `buffer_pool.h` | **Language**: C | **Type**: header\n\n```c\nFile: buffer_pool.h | Lan..."}, {"query": "struct definition", "results_count": 3, "preview": "🔍 **Codebase**: networking_project\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -0.200)\n📁 **File**: `socket_manager.cpp` | **Language**: CPP | **Type**: struct_specifier\n\n```cpp\nFile: ..."}, {"query": "template class", "results_count": 3, "preview": "🔍 **Codebase**: networking_project\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -0.497)\n📁 **File**: `socket_manager.cpp` | **Language**: CPP | **Type**: struct_specifier\n\n```cpp\nFile: ..."}, {"query": "namespace std", "results_count": 3, "preview": "🔍 **Codebase**: networking_project\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -0.280)\n📁 **File**: `socket_manager.cpp` | **Language**: CPP | **Type**: function `unknown()`\n\n```cpp\nFi..."}, {"query": "malloc free", "results_count": 3, "preview": "🔍 **Codebase**: networking_project\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -0.484)\n📁 **File**: `socket_manager.cpp` | **Language**: CPP | **Type**: function `unknown()`\n\n```cpp\nFi..."}], "success": true}, "ai_test": {"questions_tested": 3, "successful_answers": 3, "results": [{"question": "What encryption functions are available?", "success": true, "response_length": 1675}, {"question": "How is memory management handled?", "success": true, "response_length": 1945}, {"question": "What are the main classes and their purposes?", "success": true, "response_length": 2013}]}, "performance": {"average_time": 0.09256448745727539, "min_time": 0.0454866886138916, "max_time": 0.19743752479553223, "queries_tested": 5}}]}