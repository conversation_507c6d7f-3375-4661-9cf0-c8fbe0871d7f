{"timestamp": "2025-06-26T07:49:44.027868", "rag_server_url": "http://home-ai-server:5002", "test_results": [{"collection_name": "utils", "basic_test": {"collection_name": "utils", "queries_tested": 10, "successful_queries": 10, "failed_queries": 0, "query_results": [{"query": "function definition", "results_count": 3, "preview": "🔍 **Codebase**: utils\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -344.558)\n📁 **File**: `tmwtimer.c` | **Language**: C | **Type**: function `tmwtimer_applInit()`\n\n```c\nFile: tmwtimer...."}, {"query": "class declaration", "results_count": 3, "preview": "🔍 **Codebase**: utils\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -392.245)\n📁 **File**: `tmwvrsn.cs` | **Language**: CSHARP | **Type**: class `tmwvrsnVersionInfo`\n\n```csharp\nFile: tmw..."}, {"query": "error handling", "results_count": 3, "preview": "🔍 **Codebase**: utils\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -336.037)\n📁 **File**: `tmwdiag.c` | **Language**: C | **Type**: function `tmwdiag_error()`\n\n```c\nFile: tmwdiag.c | La..."}, {"query": "memory allocation", "results_count": 3, "preview": "🔍 **Codebase**: utils\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -379.779)\n📁 **File**: `tmwmem.c` | **Language**: C | **Type**: function `tmwmem_lowFree()`\n\n```c\nFile: tmwmem.c | Lan..."}, {"query": "data structure", "results_count": 3, "preview": "🔍 **Codebase**: utils\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -364.805)\n📁 **File**: `tmwmem.c` | **Language**: C | **Type**: struct_specifier\n\n```c\nFile: tmwmem.c | Language: c | ..."}, {"query": "pointer arithmetic", "results_count": 3, "preview": "🔍 **Codebase**: utils\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -392.591)\n📁 **File**: `tmwsim.c` | **Language**: C | **Type**: function `tmwsim_initBitstring()`\n\n```c\nFile: tmwsim.c..."}, {"query": "struct definition", "results_count": 3, "preview": "🔍 **Codebase**: utils\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -275.938)\n📁 **File**: `tmwtarg.h` | **Language**: C | **Type**: struct_specifier\n\n```c\nFile: tmwtarg.h | Language: c ..."}, {"query": "template class", "results_count": 3, "preview": "🔍 **Codebase**: utils\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -393.854)\n📁 **File**: `tmwdlist.h` | **Language**: C | **Type**: struct_specifier\n\n```c\nFile: tmwdlist.h | Language: ..."}, {"query": "namespace std", "results_count": 3, "preview": "🔍 **Codebase**: utils\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -341.525)\n📁 **File**: `tmwvrsn.cs` | **Language**: CSHARP | **Type**: namespace\n\n```csharp\nFile: tmwvrsn.cs | Languag..."}, {"query": "malloc free", "results_count": 3, "preview": "🔍 **Codebase**: utils\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -436.086)\n📁 **File**: `tmwmem.c` | **Language**: C | **Type**: function `tmwmem_free()`\n\n```c\nFile: tmwmem.c | Langua..."}], "success": true}, "ai_test": {"questions_tested": 3, "successful_answers": 3, "results": [{"question": "What encryption functions are available?", "success": true, "response_length": 1965}, {"question": "How is memory management handled?", "success": true, "response_length": 2299}, {"question": "What are the main classes and their purposes?", "success": true, "response_length": 2002}]}, "performance": {"average_time": 0.18231849670410155, "min_time": 0.14841461181640625, "max_time": 0.23607420921325684, "queries_tested": 5}}, {"collection_name": "test_project", "basic_test": {"success": false, "error": "Codebase selection failed"}, "ai_test": {"questions_tested": 3, "successful_answers": 3, "results": [{"question": "What encryption functions are available?", "success": true, "response_length": 90}, {"question": "How is memory management handled?", "success": true, "response_length": 90}, {"question": "What are the main classes and their purposes?", "success": true, "response_length": 90}]}, "performance": {"average_time": 0.3025832176208496, "min_time": 0.26629018783569336, "max_time": 0.3725571632385254, "queries_tested": 5}}, {"collection_name": "z80emu", "basic_test": {"success": false, "error": "Codebase selection failed"}, "ai_test": {"questions_tested": 3, "successful_answers": 3, "results": [{"question": "What encryption functions are available?", "success": true, "response_length": 84}, {"question": "How is memory management handled?", "success": true, "response_length": 84}, {"question": "What are the main classes and their purposes?", "success": true, "response_length": 84}]}, "performance": {"average_time": 0.3167427062988281, "min_time": 0.2933464050292969, "max_time": 0.34441256523132324, "queries_tested": 5}}, {"collection_name": "modbus", "basic_test": {"success": false, "error": "Codebase selection failed"}, "ai_test": {"questions_tested": 3, "successful_answers": 3, "results": [{"question": "What encryption functions are available?", "success": true, "response_length": 84}, {"question": "How is memory management handled?", "success": true, "response_length": 84}, {"question": "What are the main classes and their purposes?", "success": true, "response_length": 84}]}, "performance": {"average_time": 0.2779801368713379, "min_time": 0.2507975101470947, "max_time": 0.29385828971862793, "queries_tested": 5}}, {"collection_name": "networking_project", "basic_test": {"success": false, "error": "Codebase selection failed"}, "ai_test": {"questions_tested": 3, "successful_answers": 3, "results": [{"question": "What encryption functions are available?", "success": true, "response_length": 96}, {"question": "How is memory management handled?", "success": true, "response_length": 96}, {"question": "What are the main classes and their purposes?", "success": true, "response_length": 96}]}, "performance": {"average_time": 0.2918137550354004, "min_time": 0.2522554397583008, "max_time": 0.33896827697753906, "queries_tested": 5}}]}