import json
import sys
from typing import List, Dict, Optional
import ollama

try:
    import chromadb
    from chromadb.utils import embedding_functions
except ImportError as e:
    print(f"ChromaDB import error: {e}")
    sys.exit(1)

class OllamaEmbeddingFunction:
    """Custom embedding function that uses Ollama for generating embeddings"""

    def __init__(self, ollama_host: str = "http://localhost:11434", model_name: str = "nomic-embed-text"):
        self.ollama_host = ollama_host
        self.model_name = model_name
        self.client = ollama.Client(host=ollama_host)
        self.embedding_dim = None  # Will be detected on first use

        # Test connection and ensure model is available
        try:
            # Try to pull the embedding model if it's not available
            self.client.pull(model_name)
            print(f"✅ Ollama embedding model '{model_name}' is ready")

            # Test embedding to detect dimensions
            test_response = self.client.embeddings(model=model_name, prompt="test")
            self.embedding_dim = len(test_response.get("embedding", []))
            print(f"✅ Detected embedding dimensions: {self.embedding_dim}")

        except Exception as e:
            print(f"⚠️ Warning: Could not initialize model '{model_name}': {e}")
            print("   Make sure the model is available in Ollama")
            # Default to nomic-embed-text dimensions
            self.embedding_dim = 384

    def __call__(self, input: List[str]) -> List[List[float]]:
        """Generate embeddings for a list of texts"""
        embeddings = []

        for text in input:
            try:
                response = self.client.embeddings(model=self.model_name, prompt=text)
                embedding = response["embedding"]

                # Update dimension if not set
                if self.embedding_dim is None:
                    self.embedding_dim = len(embedding)
                    print(f"✅ Detected embedding dimensions: {self.embedding_dim}")

                embeddings.append(embedding)
            except Exception as e:
                print(f"❌ Error generating embedding for text: {e}")
                # Return a zero vector as fallback with correct dimensions
                fallback_dim = self.embedding_dim if self.embedding_dim else 384
                embeddings.append([0.0] * fallback_dim)

        return embeddings

class VectorDBCreator:
    def __init__(self, db_path="./chroma_db", ollama_host="http://localhost:11434", use_ollama=True):
        """Initialize the vector database creator with optional Ollama embeddings"""
        try:
            self.client = chromadb.PersistentClient(path=db_path)
            self.collection_name = "cpp_protocol_code"  # Default, will be updated
            self.use_ollama = use_ollama
            self.ollama_host = ollama_host
            
            # Set up embedding function
            if use_ollama:
                print(f"🔥 Initializing with Ollama embeddings (host: {ollama_host})")
                self.embedding_function = OllamaEmbeddingFunction(
                    ollama_host=ollama_host,
                    model_name="nomic-embed-text"  # Good embedding model for code
                )
            else:
                print("🔥 Using ChromaDB default embeddings")
                self.embedding_function = embedding_functions.DefaultEmbeddingFunction()
            
            print(f"Initialized ChromaDB client at {db_path}")
        except Exception as e:
            print(f"Error initializing ChromaDB client: {e}")
            raise
    
    def create_collection(self, chunks: List[Dict], collection_name: Optional[str] = None):
        """Create ChromaDB collection using Ollama or default embedding function"""
        
        if collection_name:
            self.collection_name = collection_name
        
        # Delete existing collection if it exists
        try:
            self.client.delete_collection(self.collection_name)
            print(f"Deleted existing collection: {self.collection_name}")
        except Exception:
            print(f"No existing collection to delete: {self.collection_name}")
        
        # Create new collection with custom embedding function
        try:
            collection = self.client.create_collection(
                name=self.collection_name,
                embedding_function=self.embedding_function,
                metadata={"description": "C/C++/Python/C# protocol stack source code with enhanced metadata"}
            )
            print(f"Created new collection: {self.collection_name}")
            if self.use_ollama:
                print("Using Ollama embeddings with nomic-embed-text model")
            else:
                print("Using ChromaDB's default embedding function")
        except Exception as e:
            print(f"Error creating collection: {e}")
            raise
        
        # Prepare data for insertion with enhanced content formatting
        documents = []
        metadatas = []
        ids = []
        
        for i, chunk in enumerate(chunks):
            # Enhanced document content with metadata context
            content = self._format_document_content(chunk)
            documents.append(content)
            
            # Ensure metadata is ChromaDB compatible (no nested objects)
            metadata = self._flatten_metadata(chunk['metadata'])
            metadatas.append(metadata)
            
            # Create more descriptive IDs
            chunk_id = chunk['metadata'].get('chunk_id', f'chunk_{i}')
            chunk_type = chunk['metadata'].get('type', 'unknown')
            ids.append(f"{chunk_type}_{i}_{chunk_id}")
        
        print(f"Adding {len(documents)} documents to collection...")
        if self.use_ollama:
            print("Generating embeddings using Ollama...")
        else:
            print("ChromaDB will automatically generate embeddings")
        
        # Show statistics before insertion
        self._show_chunk_statistics(chunks)
        
        # Process in batches (smaller batches when using Ollama to avoid overwhelming it)
        batch_size = 50 if self.use_ollama else 100
        total_batches = (len(documents) - 1) // batch_size + 1
        successful_batches = 0
        failed_chunks = []
        
        for i in range(0, len(documents), batch_size):
            batch_docs = documents[i:i+batch_size]
            batch_metas = metadatas[i:i+batch_size]
            batch_ids = ids[i:i+batch_size]
            
            batch_num = i // batch_size + 1
            print(f"Processing batch {batch_num}/{total_batches} ({len(batch_docs)} documents)")
            
            try:
                # ChromaDB will use our custom embedding function
                collection.add(
                    documents=batch_docs,
                    metadatas=batch_metas,
                    ids=batch_ids
                )
                print(f"✓ Successfully added batch {batch_num}")
                successful_batches += 1
                
            except Exception as e:
                print(f"✗ Error processing batch {batch_num}: {e}")
                # Track failed chunks for debugging
                for j, chunk_id in enumerate(batch_ids):
                    failed_chunks.append({
                        'id': chunk_id,
                        'error': str(e),
                        'batch': batch_num
                    })
                continue
        
        print(f"Successfully created collection with {successful_batches}/{total_batches} batches")
        print(f"Total documents in collection: {collection.count()}")
        
        if failed_chunks:
            print(f"⚠ Warning: {len(failed_chunks)} chunks failed to insert")
            # Save failed chunks for debugging
            with open("failed_chunks.json", "w") as f:
                json.dump(failed_chunks, f, indent=2)
            print("Failed chunk details saved to failed_chunks.json")
        
        return collection
    
    def _format_document_content(self, chunk: Dict) -> str:
        """Format document content with metadata context for better embeddings"""
        metadata = chunk['metadata']
        content = chunk['content']
        
        # Add contextual information to help with semantic search
        context_parts = []
        
        # Add file context
        rel_path = metadata.get('relative_path', metadata.get('filepath', ''))
        if rel_path:
            context_parts.append(f"File: {rel_path}")
        
        # Add language context
        language = metadata.get('language', 'unknown')
        context_parts.append(f"Language: {language}")
        
        # Add type-specific context
        chunk_type = metadata.get('type', 'unknown')
        context_parts.append(f"Type: {chunk_type}")
        
        # Add name-specific context if available
        if chunk_type == 'function' and 'function_name' in metadata:
            context_parts.append(f"Function: {metadata['function_name']}")
        elif chunk_type == 'class' and 'class_name' in metadata:
            context_parts.append(f"Class: {metadata['class_name']}")
            if 'method_count' in metadata:
                context_parts.append(f"Methods: {metadata['method_count']}")
        elif chunk_type == 'method' and 'class_name' in metadata and 'method_name' in metadata:
            context_parts.append(f"Method: {metadata['class_name']}::{metadata['method_name']}")
        elif chunk_type == 'namespace' and 'namespace_name' in metadata:
            context_parts.append(f"Namespace: {metadata['namespace_name']}")
        
        # Combine context with content
        context_header = " | ".join(context_parts)
        formatted_content = f"{context_header}\n\n{content}"
        
        return formatted_content
    
    def _flatten_metadata(self, metadata: Dict) -> Dict:
        """Flatten metadata to ensure ChromaDB compatibility"""
        flattened = {}
        
        for key, value in metadata.items():
            if isinstance(value, (str, int, float, bool)):
                flattened[key] = value
            elif value is None:
                flattened[key] = ""
            else:
                # Convert complex types to strings
                flattened[key] = str(value)
        
        return flattened
    
    def _show_chunk_statistics(self, chunks: List[Dict]):
        """Display statistics about the chunks being processed"""
        print("\n=== Chunk Statistics ===")
        
        # Count by type
        type_counts: Dict[str, int] = {}
        language_counts: Dict[str, int] = {}
        file_counts: Dict[str, int] = {}
        
        for chunk in chunks:
            metadata = chunk['metadata']
            
            chunk_type = metadata.get('type', 'unknown')
            type_counts[chunk_type] = type_counts.get(chunk_type, 0) + 1
            
            language = metadata.get('language', 'unknown')
            language_counts[language] = language_counts.get(language, 0) + 1
            
            filepath = metadata.get('relative_path', metadata.get('filepath', 'unknown'))
            file_counts[filepath] = file_counts.get(filepath, 0) + 1
        
        print(f"Total chunks: {len(chunks)}")
        
        print("\nBy type:")
        for chunk_type, count in sorted(type_counts.items()):
            print(f"  {chunk_type}: {count}")
        
        print("\nBy language:")
        for language, count in sorted(language_counts.items()):
            print(f"  {language}: {count}")
        
        print(f"\nFiles processed: {len(file_counts)}")
        print("========================\n")