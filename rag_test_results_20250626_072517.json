{"timestamp": "2025-06-26T07:25:17.513761", "rag_server_url": "http://home-ai-server:5002", "test_results": [{"collection_name": "utils", "basic_test": {"collection_name": "utils", "queries_tested": 10, "successful_queries": 10, "failed_queries": 0, "query_results": [{"query": "function definition", "results_count": 3, "fixed": true, "preview": "🔍 **Codebase**: utils\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -344.558)\n📁 **File**: `tmwtimer.c` | **Language**: C | **Type**: function `tmwtimer_applInit()`\n\n```c\nFile: tmwtimer...."}, {"query": "class declaration", "results_count": 3, "preview": "🔍 **Codebase**: utils\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -392.245)\n📁 **File**: `tmwvrsn.cs` | **Language**: CSHARP | **Type**: class `tmwvrsnVersionInfo`\n\n```csharp\nFile: tmw..."}, {"query": "error handling", "results_count": 3, "preview": "🔍 **Codebase**: utils\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -336.037)\n📁 **File**: `tmwdiag.c` | **Language**: C | **Type**: function `tmwdiag_error()`\n\n```c\nFile: tmwdiag.c | La..."}, {"query": "memory allocation", "results_count": 3, "preview": "🔍 **Codebase**: utils\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -379.779)\n📁 **File**: `tmwmem.c` | **Language**: C | **Type**: function `tmwmem_lowFree()`\n\n```c\nFile: tmwmem.c | Lan..."}, {"query": "data structure", "results_count": 3, "preview": "🔍 **Codebase**: utils\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -360.794)\n📁 **File**: `tmwcrypto.c` | **Language**: C | **Type**: struct_specifier\n\n```c\nFile: tmwcrypto.c | Language..."}, {"query": "pointer arithmetic", "results_count": 3, "preview": "🔍 **Codebase**: utils\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -392.591)\n📁 **File**: `tmwsim.c` | **Language**: C | **Type**: function `tmwsim_initBitstring()`\n\n```c\nFile: tmwsim.c..."}, {"query": "struct definition", "results_count": 3, "preview": "🔍 **Codebase**: utils\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -275.938)\n📁 **File**: `tmwtarg.h` | **Language**: C | **Type**: struct_specifier\n\n```c\nFile: tmwtarg.h | Language: c ..."}, {"query": "template class", "results_count": 3, "preview": "🔍 **Codebase**: utils\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -393.854)\n📁 **File**: `tmwdlist.h` | **Language**: C | **Type**: struct_specifier\n\n```c\nFile: tmwdlist.h | Language: ..."}, {"query": "namespace std", "results_count": 3, "preview": "🔍 **Codebase**: utils\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -341.525)\n📁 **File**: `tmwvrsn.cs` | **Language**: CSHARP | **Type**: namespace\n\n```csharp\nFile: tmwvrsn.cs | Languag..."}, {"query": "malloc free", "results_count": 3, "preview": "🔍 **Codebase**: utils\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -436.086)\n📁 **File**: `tmwmem.c` | **Language**: C | **Type**: function `tmwmem_free()`\n\n```c\nFile: tmwmem.c | Langua..."}], "success": true}, "ai_test": {"questions_tested": 3, "successful_answers": 3, "results": [{"question": "What encryption functions are available?", "success": true, "response_length": 1911}, {"question": "How is memory management handled?", "success": true, "response_length": 2486}, {"question": "What are the main classes and their purposes?", "success": true, "response_length": 1522}]}, "performance": {"average_time": 0.07210755348205566, "min_time": 0.0430300235748291, "max_time": 0.16078972816467285, "queries_tested": 5}}, {"collection_name": "test_project", "basic_test": {"collection_name": "test_project", "queries_tested": 10, "successful_queries": 10, "failed_queries": 0, "query_results": [{"query": "function definition", "results_count": 3, "preview": "🔍 **Codebase**: test_project\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -0.216)\n📁 **File**: `main.cpp` | **Language**: CPP | **Type**: function `unknown()`\n\n```cpp\nFile: main.cpp | L..."}, {"query": "class declaration", "results_count": 3, "preview": "🔍 **Codebase**: test_project\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: 0.099)\n📁 **File**: `main.cpp` | **Language**: CPP | **Type**: class `TestClass`\n\n```cpp\nFile: main.cpp | Langu..."}, {"query": "error handling", "results_count": 3, "preview": "🔍 **Codebase**: test_project\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -0.671)\n📁 **File**: `main.cpp` | **Language**: CPP | **Type**: method `TestClass::unknown()`\n\n```cpp\nFile: mai..."}, {"query": "memory allocation", "results_count": 3, "preview": "🔍 **Codebase**: test_project\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -0.798)\n📁 **File**: `utils.cpp` | **Language**: CPP | **Type**: header\n\n```cpp\nFile: utils.cpp | Language: cpp..."}, {"query": "data structure", "results_count": 3, "preview": "🔍 **Codebase**: test_project\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -0.583)\n📁 **File**: `utils.cpp` | **Language**: CPP | **Type**: header\n\n```cpp\nFile: utils.cpp | Language: cpp..."}, {"query": "pointer arithmetic", "results_count": 3, "preview": "🔍 **Codebase**: test_project\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -0.486)\n📁 **File**: `utils.cpp` | **Language**: CPP | **Type**: header\n\n```cpp\nFile: utils.cpp | Language: cpp..."}, {"query": "struct definition", "results_count": 3, "preview": "🔍 **Codebase**: test_project\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -0.535)\n📁 **File**: `main.cpp` | **Language**: CPP | **Type**: method `TestClass::TestClass()`\n\n```cpp\nFile: m..."}, {"query": "template class", "results_count": 3, "preview": "🔍 **Codebase**: test_project\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -0.119)\n📁 **File**: `main.cpp` | **Language**: CPP | **Type**: class `TestClass`\n\n```cpp\nFile: main.cpp | Lang..."}, {"query": "namespace std", "results_count": 3, "preview": "🔍 **Codebase**: test_project\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: 0.127)\n📁 **File**: `utils.cpp` | **Language**: CPP | **Type**: namespace\n\n```cpp\nFile: utils.cpp | Language: c..."}, {"query": "malloc free", "results_count": 3, "preview": "🔍 **Codebase**: test_project\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -0.818)\n📁 **File**: `utils.cpp` | **Language**: CPP | **Type**: header\n\n```cpp\nFile: utils.cpp | Language: cpp..."}], "success": true}, "ai_test": {"questions_tested": 3, "successful_answers": 3, "results": [{"question": "What encryption functions are available?", "success": true, "response_length": 1530}, {"question": "How is memory management handled?", "success": true, "response_length": 1640}, {"question": "What are the main classes and their purposes?", "success": true, "response_length": 1197}]}, "performance": {"average_time": 0.10420756340026856, "min_time": 0.04411005973815918, "max_time": 0.2179090976715088, "queries_tested": 5}}, {"collection_name": "z80emu", "basic_test": {"collection_name": "z80emu", "queries_tested": 10, "successful_queries": 10, "failed_queries": 0, "query_results": [{"query": "function definition", "results_count": 3, "preview": "🔍 **Codebase**: z80emu\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -0.334)\n📁 **File**: `Essenbee.Z80/Z80.cs` | **Language**: CSHARP | **Type**: function `tFlag(F()`\n\n```csharp\nFile: E..."}, {"query": "class declaration", "results_count": 3, "preview": "🔍 **Codebase**: z80emu\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -0.302)\n📁 **File**: `Essenbee.Z80.Debugger/App.xaml.cs` | **Language**: CSHARP | **Type**: class `p :`\n\n```csharp\nFi..."}, {"query": "error handling", "results_count": 3, "preview": "🔍 **Codebase**: z80emu\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -0.452)\n📁 **File**: `Essenbee.Z80/Z80.Instructions.cs` | **Language**: CSHARP | **Type**: function `P, B()`\n\n```csha..."}, {"query": "memory allocation", "results_count": 3, "preview": "🔍 **Codebase**: z80emu\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -0.495)\n📁 **File**: `Essenbee.Z80.Tests/JumpGroupShould.cs` | **Language**: CSHARP | **Type**: method `mpGroupShould..."}, {"query": "data structure", "results_count": 3, "preview": "🔍 **Codebase**: z80emu\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -0.492)\n📁 **File**: `Essenbee.Spectrum48/Z80Snapshot.cs` | **Language**: CSHARP | **Type**: header\n\n```csharp\nFile: ..."}, {"query": "pointer arithmetic", "results_count": 3, "preview": "🔍 **Codebase**: z80emu\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -0.291)\n📁 **File**: `Essenbee.Z80/Z80.ArithmeticLogic.cs` | **Language**: CSHARP | **Type**: method `0\n ::CAR(b()`\n\n..."}, {"query": "struct definition", "results_count": 3, "preview": "🔍 **Codebase**: z80emu\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -0.660)\n📁 **File**: `Essenbee.Z80.Tests/SixteenBitArithmeticLogicGroupShould.cs` | **Language**: CSHARP | **Type**: ..."}, {"query": "template class", "results_count": 3, "preview": "🔍 **Codebase**: z80emu\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -0.386)\n📁 **File**: `Essenbee.Z80.Tests/Classes/BasicBus.cs` | **Language**: CSHARP | **Type**: using_directive\n\n```..."}, {"query": "namespace std", "results_count": 3, "preview": "🔍 **Codebase**: z80emu\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -0.623)\n📁 **File**: `Essenbee.Z80.Tests/SixteenBitLoadGroupShould.cs` | **Language**: CSHARP | **Type**: namespace\n\n..."}, {"query": "malloc free", "results_count": 3, "preview": "🔍 **Codebase**: z80emu\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -0.593)\n📁 **File**: `Essenbee.Z80.Tests/JumpGroupShould.cs` | **Language**: CSHARP | **Type**: method `mpGroupShould..."}], "success": true}, "ai_test": {"questions_tested": 3, "successful_answers": 2, "results": [{"question": "What encryption functions are available?", "success": true, "response_length": 1770}, {"question": "How is memory management handled?", "success": true, "response_length": 2531}, {"question": "What are the main classes and their purposes?", "success": false, "error": "HTTPConnectionPool(host='home-ai-server', port=5002): Max retries exceeded with url: /tools/ask_about_code (Caused by NameResolutionError(\"<urllib3.connection.HTTPConnection object at 0x0000018292F5F490>: Failed to resolve 'home-ai-server' ([Errno 11004] getaddrinfo failed)\"))"}]}, "performance": {"error": "No successful queries"}}, {"collection_name": "modbus", "basic_test": {"success": false, "error": "Failed to select codebase: HTTPConnectionPool(host='home-ai-server', port=5002): Max retries exceeded with url: /tools/select_codebase (Caused by NameResolutionError(\"<urllib3.connection.HTTPConnection object at 0x0000018292F677D0>: Failed to resolve 'home-ai-server' ([Errno 11004] getaddrinfo failed)\"))"}, "ai_test": {"questions_tested": 3, "successful_answers": 0, "results": [{"question": "What encryption functions are available?", "success": false, "error": "HTTPConnectionPool(host='home-ai-server', port=5002): Max retries exceeded with url: /tools/ask_about_code (Caused by NameResolutionError(\"<urllib3.connection.HTTPConnection object at 0x0000018292F74E50>: Failed to resolve 'home-ai-server' ([Errno 11004] getaddrinfo failed)\"))"}, {"question": "How is memory management handled?", "success": false, "error": "HTTPConnectionPool(host='home-ai-server', port=5002): Max retries exceeded with url: /tools/ask_about_code (Caused by NameResolutionError(\"<urllib3.connection.HTTPConnection object at 0x0000018292F77110>: Failed to resolve 'home-ai-server' ([Errno 11004] getaddrinfo failed)\"))"}, {"question": "What are the main classes and their purposes?", "success": false, "error": "HTTPConnectionPool(host='home-ai-server', port=5002): Max retries exceeded with url: /tools/ask_about_code (Caused by NameResolutionError(\"<urllib3.connection.HTTPConnection object at 0x00000182927EEB50>: Failed to resolve 'home-ai-server' ([Errno 11004] getaddrinfo failed)\"))"}]}, "performance": {"error": "No successful queries"}}, {"collection_name": "networking_project", "basic_test": {"success": false, "error": "Failed to select codebase: HTTPConnectionPool(host='home-ai-server', port=5002): Max retries exceeded with url: /tools/select_codebase (Caused by NameResolutionError(\"<urllib3.connection.HTTPConnection object at 0x0000018292F6AB90>: Failed to resolve 'home-ai-server' ([Errno 11004] getaddrinfo failed)\"))"}, "ai_test": {"questions_tested": 3, "successful_answers": 0, "results": [{"question": "What encryption functions are available?", "success": false, "error": "HTTPConnectionPool(host='home-ai-server', port=5002): Max retries exceeded with url: /tools/ask_about_code (Caused by NameResolutionError(\"<urllib3.connection.HTTPConnection object at 0x0000018292F69B10>: Failed to resolve 'home-ai-server' ([Errno 11004] getaddrinfo failed)\"))"}, {"question": "How is memory management handled?", "success": false, "error": "HTTPConnectionPool(host='home-ai-server', port=5002): Max retries exceeded with url: /tools/ask_about_code (Caused by NameResolutionError(\"<urllib3.connection.HTTPConnection object at 0x0000018292F5E890>: Failed to resolve 'home-ai-server' ([Errno 11004] getaddrinfo failed)\"))"}, {"question": "What are the main classes and their purposes?", "success": false, "error": "HTTPConnectionPool(host='home-ai-server', port=5002): Max retries exceeded with url: /tools/ask_about_code (Caused by NameResolutionError(\"<urllib3.connection.HTTPConnection object at 0x0000018292F75450>: Failed to resolve 'home-ai-server' ([Errno 11004] getaddrinfo failed)\"))"}]}, "performance": {"error": "No successful queries"}}]}