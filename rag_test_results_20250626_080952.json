{"timestamp": "2025-06-26T08:09:52.982789", "rag_server_url": "http://home-ai-server:5002", "test_results": [{"collection_name": "utils", "basic_test": {"collection_name": "utils", "queries_tested": 10, "successful_queries": 10, "failed_queries": 0, "query_results": [{"query": "function definition", "results_count": 3, "preview": "🔍 **Codebase**: utils\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -344.558)\n📁 **File**: `tmwtimer.c` | **Language**: C | **Type**: function `tmwtimer_applInit()`\n\n```c\nFile: tmwtimer...."}, {"query": "class declaration", "results_count": 3, "preview": "🔍 **Codebase**: utils\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -392.245)\n📁 **File**: `tmwvrsn.cs` | **Language**: CSHARP | **Type**: class `tmwvrsnVersionInfo`\n\n```csharp\nFile: tmw..."}, {"query": "error handling", "results_count": 3, "preview": "🔍 **Codebase**: utils\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -336.037)\n📁 **File**: `tmwdiag.c` | **Language**: C | **Type**: function `tmwdiag_error()`\n\n```c\nFile: tmwdiag.c | La..."}, {"query": "memory allocation", "results_count": 3, "preview": "🔍 **Codebase**: utils\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -379.779)\n📁 **File**: `tmwmem.c` | **Language**: C | **Type**: function `tmwmem_lowFree()`\n\n```c\nFile: tmwmem.c | Lan..."}, {"query": "data structure", "results_count": 3, "preview": "🔍 **Codebase**: utils\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -364.805)\n📁 **File**: `tmwmem.c` | **Language**: C | **Type**: struct_specifier\n\n```c\nFile: tmwmem.c | Language: c | ..."}, {"query": "pointer arithmetic", "results_count": 3, "preview": "🔍 **Codebase**: utils\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -392.591)\n📁 **File**: `tmwsim.c` | **Language**: C | **Type**: function `tmwsim_initBitstring()`\n\n```c\nFile: tmwsim.c..."}, {"query": "struct definition", "results_count": 3, "preview": "🔍 **Codebase**: utils\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -275.938)\n📁 **File**: `tmwtarg.h` | **Language**: C | **Type**: struct_specifier\n\n```c\nFile: tmwtarg.h | Language: c ..."}, {"query": "template class", "results_count": 3, "preview": "🔍 **Codebase**: utils\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -393.854)\n📁 **File**: `tmwdlist.h` | **Language**: C | **Type**: struct_specifier\n\n```c\nFile: tmwdlist.h | Language: ..."}, {"query": "namespace std", "results_count": 3, "preview": "🔍 **Codebase**: utils\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -341.525)\n📁 **File**: `tmwvrsn.cs` | **Language**: CSHARP | **Type**: namespace\n\n```csharp\nFile: tmwvrsn.cs | Languag..."}, {"query": "malloc free", "results_count": 3, "preview": "🔍 **Codebase**: utils\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -436.086)\n📁 **File**: `tmwmem.c` | **Language**: C | **Type**: function `tmwmem_free()`\n\n```c\nFile: tmwmem.c | Langua..."}], "success": true}, "ai_test": {"questions_tested": 3, "successful_answers": 3, "results": [{"question": "What encryption functions are available?", "success": true, "response_length": 2134}, {"question": "How is memory management handled?", "success": true, "response_length": 2422}, {"question": "What are the main classes and their purposes?", "success": true, "response_length": 1869}]}, "performance": {"average_time": 0.1638798713684082, "min_time": 0.1305093765258789, "max_time": 0.2256608009338379, "queries_tested": 5}}, {"collection_name": "test_project", "basic_test": {"collection_name": "test_project", "queries_tested": 10, "successful_queries": 10, "failed_queries": 0, "query_results": [{"query": "function definition", "results_count": 3, "preview": "🔍 **Codebase**: test_project\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -354.706)\n📁 **File**: `main.cpp` | **Language**: CPP | **Type**: function `main()`\n\n```cpp\nFile: main.cpp | La..."}, {"query": "class declaration", "results_count": 3, "preview": "🔍 **Codebase**: test_project\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -366.211)\n📁 **File**: `main.cpp` | **Language**: CPP | **Type**: class `TestClass`\n\n```cpp\nFile: main.cpp | La..."}, {"query": "error handling", "results_count": 3, "preview": "🔍 **Codebase**: test_project\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -437.667)\n📁 **File**: `main.cpp` | **Language**: CPP | **Type**: function `main()`\n\n```cpp\nFile: main.cpp | La..."}, {"query": "memory allocation", "results_count": 3, "preview": "🔍 **Codebase**: test_project\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -475.590)\n📁 **File**: `utils.cpp` | **Language**: CPP | **Type**: header\n\n```cpp\nFile: utils.cpp | Language: c..."}, {"query": "data structure", "results_count": 3, "preview": "🔍 **Codebase**: test_project\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -440.244)\n📁 **File**: `utils.cpp` | **Language**: CPP | **Type**: namespace\n\n```cpp\nFile: utils.cpp | Language..."}, {"query": "pointer arithmetic", "results_count": 3, "preview": "🔍 **Codebase**: test_project\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -455.457)\n📁 **File**: `utils.cpp` | **Language**: CPP | **Type**: header\n\n```cpp\nFile: utils.cpp | Language: c..."}, {"query": "struct definition", "results_count": 3, "preview": "🔍 **Codebase**: test_project\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -434.175)\n📁 **File**: `main.cpp` | **Language**: CPP | **Type**: function `TestClass()`\n\n```cpp\nFile: main.cpp..."}, {"query": "template class", "results_count": 3, "preview": "🔍 **Codebase**: test_project\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -397.338)\n📁 **File**: `main.cpp` | **Language**: CPP | **Type**: function `TestClass()`\n\n```cpp\nFile: main.cpp..."}, {"query": "namespace std", "results_count": 3, "preview": "🔍 **Codebase**: test_project\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -282.755)\n📁 **File**: `utils.cpp` | **Language**: CPP | **Type**: namespace\n\n```cpp\nFile: utils.cpp | Language..."}, {"query": "malloc free", "results_count": 3, "preview": "🔍 **Codebase**: test_project\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -558.712)\n📁 **File**: `utils.cpp` | **Language**: CPP | **Type**: header\n\n```cpp\nFile: utils.cpp | Language: c..."}], "success": true}, "ai_test": {"questions_tested": 3, "successful_answers": 3, "results": [{"question": "What encryption functions are available?", "success": true, "response_length": 1683}, {"question": "How is memory management handled?", "success": true, "response_length": 2165}, {"question": "What are the main classes and their purposes?", "success": true, "response_length": 1354}]}, "performance": {"average_time": 0.17365636825561523, "min_time": 0.12247753143310547, "max_time": 0.2763810157775879, "queries_tested": 5}}, {"collection_name": "z80emu", "basic_test": {"collection_name": "z80emu", "queries_tested": 10, "successful_queries": 10, "failed_queries": 0, "query_results": [{"query": "function definition", "results_count": 3, "preview": "🔍 **Codebase**: z80emu\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -344.158)\n📁 **File**: `Essenbee.Z80.Debugger/ObservableDictionary.cs` | **Language**: CSHARP | **Type**: function `d..."}, {"query": "class declaration", "results_count": 3, "preview": "🔍 **Codebase**: z80emu\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -361.769)\n📁 **File**: `Essenbee.Z80.Debugger/CpuStatus.xaml.cs` | **Language**: CSHARP | **Type**: class `uStatus :`..."}, {"query": "error handling", "results_count": 3, "preview": "🔍 **Codebase**: z80emu\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -358.264)\n📁 **File**: `Essenbee.Z80.Debugger/ObservableDictionary.cs` | **Language**: CSHARP | **Type**: method `ser..."}, {"query": "memory allocation", "results_count": 3, "preview": "🔍 **Codebase**: z80emu\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -321.544)\n📁 **File**: `Essenbee.Z80.Debugger/Generated_ViewModel.cs` | **Language**: CSHARP | **Type**: function `an..."}, {"query": "data structure", "results_count": 3, "preview": "🔍 **Codebase**: z80emu\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -396.548)\n📁 **File**: `Essenbee.Z80.Debugger/ObservableDictionary.cs` | **Language**: CSHARP | **Type**: function `t..."}, {"query": "pointer arithmetic", "results_count": 3, "preview": "🔍 **Codebase**: z80emu\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -385.894)\n📁 **File**: `Essenbee.Z80.Tests/Z80EmulatorShould.cs` | **Language**: CSHARP | **Type**: function `ecuteEi..."}, {"query": "struct definition", "results_count": 3, "preview": "🔍 **Codebase**: z80emu\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -362.108)\n📁 **File**: `Essenbee.Z80/Instruction.cs` | **Language**: CSHARP | **Type**: class `struction\n `\n\n```cshar..."}, {"query": "template class", "results_count": 3, "preview": "🔍 **Codebase**: z80emu\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -376.972)\n📁 **File**: `Essenbee.Z80.Debugger/ControlPanel.xaml.cs` | **Language**: CSHARP | **Type**: class `ntrolPa..."}, {"query": "namespace std", "results_count": 3, "preview": "🔍 **Codebase**: z80emu\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -316.920)\n📁 **File**: `Essenbee.Z80.Debugger/StackDisplay.xaml.cs` | **Language**: CSHARP | **Type**: namespace\n\n```..."}, {"query": "malloc free", "results_count": 3, "preview": "🔍 **Codebase**: z80emu\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -520.305)\n📁 **File**: `Essenbee.Z80.Debugger/Generated_ViewModel.cs` | **Language**: CSHARP | **Type**: function `is..."}], "success": true}, "ai_test": {"questions_tested": 3, "successful_answers": 3, "results": [{"question": "What encryption functions are available?", "success": true, "response_length": 2161}, {"question": "How is memory management handled?", "success": true, "response_length": 2120}, {"question": "What are the main classes and their purposes?", "success": true, "response_length": 2098}]}, "performance": {"average_time": 0.18942384719848632, "min_time": 0.1459190845489502, "max_time": 0.27185559272766113, "queries_tested": 5}}, {"collection_name": "modbus", "basic_test": {"collection_name": "modbus", "queries_tested": 10, "successful_queries": 10, "failed_queries": 0, "query_results": [{"query": "function definition", "results_count": 3, "preview": "🔍 **Codebase**: modbus\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -335.758)\n📁 **File**: `smbdata.c` | **Language**: C | **Type**: function `smbdata_diagClearCount()`\n\n```c\nFile: smbd..."}, {"query": "class declaration", "results_count": 3, "preview": "🔍 **Codebase**: modbus\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -403.239)\n📁 **File**: `mbplink.c` | **Language**: C | **Type**: function `mbplink_initConfig()`\n\n```c\nFile: mbplink...."}, {"query": "error handling", "results_count": 3, "preview": "🔍 **Codebase**: modbus\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -347.032)\n📁 **File**: `smbsesn.c` | **Language**: C | **Type**: function `_processReadExceptionStatus()`\n\n```c\nFile:..."}, {"query": "memory allocation", "results_count": 3, "preview": "🔍 **Codebase**: modbus\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -352.886)\n📁 **File**: `mbplink.c` | **Language**: C | **Type**: function `mbplink_getNeededBytes()`\n\n```c\nFile: mbpl..."}, {"query": "data structure", "results_count": 3, "preview": "🔍 **Codebase**: modbus\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -357.404)\n📁 **File**: `mmbmem.c` | **Language**: C | **Type**: struct_specifier\n\n```c\nFile: mmbmem.c | Language: c |..."}, {"query": "pointer arithmetic", "results_count": 3, "preview": "🔍 **Codebase**: modbus\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -382.769)\n📁 **File**: `smbsim.c` | **Language**: C | **Type**: function `smbsim_getPointNumber()`\n\n```c\nFile: smbsim..."}, {"query": "struct definition", "results_count": 3, "preview": "🔍 **Codebase**: modbus\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -286.391)\n📁 **File**: `mbtlink.c` | **Language**: C | **Type**: struct_specifier\n\n```c\nFile: mbtlink.c | Language: c..."}, {"query": "template class", "results_count": 3, "preview": "🔍 **Codebase**: modbus\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -395.829)\n📁 **File**: `mbchnl.h` | **Language**: C | **Type**: struct_specifier\n\n```c\nFile: mbchnl.h | Language: c |..."}, {"query": "namespace std", "results_count": 3, "preview": "🔍 **Codebase**: modbus\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -351.720)\n📁 **File**: `smbmem.h` | **Language**: C | **Type**: struct_specifier\n\n```c\nFile: smbmem.h | Language: c |..."}, {"query": "malloc free", "results_count": 3, "preview": "🔍 **Codebase**: modbus\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -417.186)\n📁 **File**: `mbmem.c` | **Language**: C | **Type**: function `mbmem_free()`\n\n```c\nFile: mbmem.c | Language..."}], "success": true}, "ai_test": {"questions_tested": 3, "successful_answers": 3, "results": [{"question": "What encryption functions are available?", "success": true, "response_length": 1859}, {"question": "How is memory management handled?", "success": true, "response_length": 1718}, {"question": "What are the main classes and their purposes?", "success": true, "response_length": 2008}]}, "performance": {"average_time": 0.18789525032043458, "min_time": 0.1548631191253662, "max_time": 0.21812152862548828, "queries_tested": 5}}, {"collection_name": "networking_project", "basic_test": {"collection_name": "networking_project", "queries_tested": 10, "successful_queries": 10, "failed_queries": 0, "query_results": [{"query": "function definition", "results_count": 3, "preview": "🔍 **Codebase**: networking_project\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -358.591)\n📁 **File**: `socket_manager.cpp` | **Language**: CPP | **Type**: function `unknown()`\n\n```cpp\n..."}, {"query": "class declaration", "results_count": 3, "preview": "🔍 **Codebase**: networking_project\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -424.776)\n📁 **File**: `socket_manager.cpp` | **Language**: CPP | **Type**: struct_specifier\n\n```cpp\nFile..."}, {"query": "error handling", "results_count": 3, "preview": "🔍 **Codebase**: networking_project\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -420.403)\n📁 **File**: `socket_manager.cpp` | **Language**: CPP | **Type**: method `SocketManager::unknow..."}, {"query": "memory allocation", "results_count": 3, "preview": "🔍 **Codebase**: networking_project\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -360.755)\n📁 **File**: `socket_manager.cpp` | **Language**: CPP | **Type**: function `unknown()`\n\n```cpp\n..."}, {"query": "data structure", "results_count": 3, "preview": "🔍 **Codebase**: networking_project\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -377.576)\n📁 **File**: `socket_manager.cpp` | **Language**: CPP | **Type**: struct_specifier\n\n```cpp\nFile..."}, {"query": "pointer arithmetic", "results_count": 3, "preview": "🔍 **Codebase**: networking_project\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -419.210)\n📁 **File**: `socket_manager.cpp` | **Language**: CPP | **Type**: function `unknown()`\n\n```cpp\n..."}, {"query": "struct definition", "results_count": 3, "preview": "🔍 **Codebase**: networking_project\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -317.785)\n📁 **File**: `socket_manager.cpp` | **Language**: CPP | **Type**: struct_specifier\n\n```cpp\nFile..."}, {"query": "template class", "results_count": 3, "preview": "🔍 **Codebase**: networking_project\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -421.946)\n📁 **File**: `socket_manager.cpp` | **Language**: CPP | **Type**: struct_specifier\n\n```cpp\nFile..."}, {"query": "namespace std", "results_count": 3, "preview": "🔍 **Codebase**: networking_project\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -346.553)\n📁 **File**: `socket_manager.cpp` | **Language**: CPP | **Type**: struct_specifier\n\n```cpp\nFile..."}, {"query": "malloc free", "results_count": 3, "preview": "🔍 **Codebase**: networking_project\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -496.243)\n📁 **File**: `socket_manager.cpp` | **Language**: CPP | **Type**: function `unknown()`\n\n```cpp\n..."}], "success": true}, "ai_test": {"questions_tested": 3, "successful_answers": 3, "results": [{"question": "What encryption functions are available?", "success": true, "response_length": 1427}, {"question": "How is memory management handled?", "success": true, "response_length": 1658}, {"question": "What are the main classes and their purposes?", "success": true, "response_length": 1429}]}, "performance": {"average_time": 0.1634070873260498, "min_time": 0.12035441398620605, "max_time": 0.2899153232574463, "queries_tested": 5}}]}