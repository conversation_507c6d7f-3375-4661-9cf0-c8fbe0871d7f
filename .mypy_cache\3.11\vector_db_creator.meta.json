{"data_mtime": 1750899214, "dep_lines": [8, 8, 1, 2, 3, 4, 7, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 20, 10, 10, 5, 10, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["chromadb.utils.embedding_functions", "chromadb.utils", "json", "sys", "typing", "ollama", "chromadb", "builtins", "_collections_abc", "_frozen_importlib", "_io", "_typeshed", "abc", "chromadb.api", "chromadb.api.types", "chromadb.config", "io", "json.encoder", "ollama._client", "ollama._types", "os", "pydantic", "pydantic._internal", "pydantic._internal._model_construction", "pydantic.main", "types", "typing_extensions"], "hash": "20af75336aed2d52b1b990c5f74d41a3222b3949", "id": "vector_db_creator", "ignore_all": false, "interface_hash": "4ff356df58a22fd763ffbce39334f33a652c93be", "mtime": 1750899208, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\home-repos\\openwebui_rag_code_server\\vector_db_creator.py", "plugin_data": null, "size": 11733, "suppressed": [], "version_id": "1.15.0"}