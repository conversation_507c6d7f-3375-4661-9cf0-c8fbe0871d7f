{"timestamp": "2025-06-25T22:11:48.081261", "rag_server_url": "http://home-ai-server:5002", "test_results": [{"collection_name": "utils", "basic_test": {"collection_name": "utils", "queries_tested": 10, "successful_queries": 10, "failed_queries": 0, "query_results": [{"query": "function definition", "results_count": 0, "preview": "❌ Error searching code: Embedding dimension 384 does not match collection dimensionality 768"}, {"query": "class declaration", "results_count": 0, "preview": "❌ Error searching code: Embedding dimension 384 does not match collection dimensionality 768"}, {"query": "error handling", "results_count": 0, "preview": "❌ Error searching code: Embedding dimension 384 does not match collection dimensionality 768"}, {"query": "memory allocation", "results_count": 0, "preview": "❌ Error searching code: Embedding dimension 384 does not match collection dimensionality 768"}, {"query": "data structure", "results_count": 0, "preview": "❌ Error searching code: Embedding dimension 384 does not match collection dimensionality 768"}, {"query": "pointer arithmetic", "results_count": 0, "preview": "❌ Error searching code: Embedding dimension 384 does not match collection dimensionality 768"}, {"query": "struct definition", "results_count": 0, "preview": "❌ Error searching code: Embedding dimension 384 does not match collection dimensionality 768"}, {"query": "template class", "results_count": 0, "preview": "❌ Error searching code: Embedding dimension 384 does not match collection dimensionality 768"}, {"query": "namespace std", "results_count": 0, "preview": "❌ Error searching code: Embedding dimension 384 does not match collection dimensionality 768"}, {"query": "malloc free", "results_count": 0, "preview": "❌ Error searching code: Embedding dimension 384 does not match collection dimensionality 768"}], "success": true}, "ai_test": {"questions_tested": 3, "successful_answers": 3, "results": [{"question": "What encryption functions are available?", "success": true, "response_length": 95}, {"question": "How is memory management handled?", "success": true, "response_length": 95}, {"question": "What are the main classes and their purposes?", "success": true, "response_length": 95}]}, "performance": {"average_time": 0.06736545562744141, "min_time": 0.0475306510925293, "max_time": 0.08556842803955078, "queries_tested": 5}}, {"collection_name": "test_project", "basic_test": {"collection_name": "test_project", "queries_tested": 10, "successful_queries": 10, "failed_queries": 0, "query_results": [{"query": "function definition", "results_count": 3, "preview": "🔍 **Codebase**: test_project\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -0.216)\n📁 **File**: `main.cpp` | **Language**: CPP | **Type**: function `unknown()`\n\n```cpp\nFile: main.cpp | L..."}, {"query": "class declaration", "results_count": 3, "preview": "🔍 **Codebase**: test_project\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: 0.099)\n📁 **File**: `main.cpp` | **Language**: CPP | **Type**: class `TestClass`\n\n```cpp\nFile: main.cpp | Langu..."}, {"query": "error handling", "results_count": 3, "preview": "🔍 **Codebase**: test_project\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -0.671)\n📁 **File**: `main.cpp` | **Language**: CPP | **Type**: method `TestClass::unknown()`\n\n```cpp\nFile: mai..."}, {"query": "memory allocation", "results_count": 3, "preview": "🔍 **Codebase**: test_project\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -0.798)\n📁 **File**: `utils.cpp` | **Language**: CPP | **Type**: header\n\n```cpp\nFile: utils.cpp | Language: cpp..."}, {"query": "data structure", "results_count": 3, "preview": "🔍 **Codebase**: test_project\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -0.583)\n📁 **File**: `utils.cpp` | **Language**: CPP | **Type**: header\n\n```cpp\nFile: utils.cpp | Language: cpp..."}, {"query": "pointer arithmetic", "results_count": 3, "preview": "🔍 **Codebase**: test_project\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -0.486)\n📁 **File**: `utils.cpp` | **Language**: CPP | **Type**: header\n\n```cpp\nFile: utils.cpp | Language: cpp..."}, {"query": "struct definition", "results_count": 3, "preview": "🔍 **Codebase**: test_project\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -0.535)\n📁 **File**: `main.cpp` | **Language**: CPP | **Type**: method `TestClass::TestClass()`\n\n```cpp\nFile: m..."}, {"query": "template class", "results_count": 3, "preview": "🔍 **Codebase**: test_project\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -0.119)\n📁 **File**: `main.cpp` | **Language**: CPP | **Type**: class `TestClass`\n\n```cpp\nFile: main.cpp | Lang..."}, {"query": "namespace std", "results_count": 3, "preview": "🔍 **Codebase**: test_project\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: 0.127)\n📁 **File**: `utils.cpp` | **Language**: CPP | **Type**: namespace\n\n```cpp\nFile: utils.cpp | Language: c..."}, {"query": "malloc free", "results_count": 3, "preview": "🔍 **Codebase**: test_project\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -0.818)\n📁 **File**: `utils.cpp` | **Language**: CPP | **Type**: header\n\n```cpp\nFile: utils.cpp | Language: cpp..."}], "success": true}, "ai_test": {"questions_tested": 3, "successful_answers": 3, "results": [{"question": "What encryption functions are available?", "success": true, "response_length": 1830}, {"question": "How is memory management handled?", "success": true, "response_length": 1781}, {"question": "What are the main classes and their purposes?", "success": true, "response_length": 1192}]}, "performance": {"average_time": 0.06870317459106445, "min_time": 0.047556400299072266, "max_time": 0.13423418998718262, "queries_tested": 5}}, {"collection_name": "z80emu", "basic_test": {"collection_name": "z80emu", "queries_tested": 10, "successful_queries": 10, "failed_queries": 0, "query_results": [{"query": "function definition", "results_count": 3, "preview": "🔍 **Codebase**: z80emu\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -0.334)\n📁 **File**: `Essenbee.Z80/Z80.cs` | **Language**: CSHARP | **Type**: function `tFlag(F()`\n\n```csharp\nFile: E..."}, {"query": "class declaration", "results_count": 3, "preview": "🔍 **Codebase**: z80emu\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -0.302)\n📁 **File**: `Essenbee.Z80.Debugger/App.xaml.cs` | **Language**: CSHARP | **Type**: class `p :`\n\n```csharp\nFi..."}, {"query": "error handling", "results_count": 3, "preview": "🔍 **Codebase**: z80emu\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -0.452)\n📁 **File**: `Essenbee.Z80/Z80.Instructions.cs` | **Language**: CSHARP | **Type**: function `P, B()`\n\n```csha..."}, {"query": "memory allocation", "results_count": 3, "preview": "🔍 **Codebase**: z80emu\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -0.495)\n📁 **File**: `Essenbee.Z80.Tests/JumpGroupShould.cs` | **Language**: CSHARP | **Type**: method `mpGroupShould..."}, {"query": "data structure", "results_count": 3, "preview": "🔍 **Codebase**: z80emu\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -0.492)\n📁 **File**: `Essenbee.Spectrum48/Z80Snapshot.cs` | **Language**: CSHARP | **Type**: header\n\n```csharp\nFile: ..."}, {"query": "pointer arithmetic", "results_count": 3, "preview": "🔍 **Codebase**: z80emu\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -0.291)\n📁 **File**: `Essenbee.Z80/Z80.ArithmeticLogic.cs` | **Language**: CSHARP | **Type**: method `0\n ::CAR(b()`\n\n..."}, {"query": "struct definition", "results_count": 3, "preview": "🔍 **Codebase**: z80emu\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -0.660)\n📁 **File**: `Essenbee.Z80.Tests/SixteenBitArithmeticLogicGroupShould.cs` | **Language**: CSHARP | **Type**: ..."}, {"query": "template class", "results_count": 3, "preview": "🔍 **Codebase**: z80emu\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -0.386)\n📁 **File**: `Essenbee.Z80.Tests/Classes/BasicBus.cs` | **Language**: CSHARP | **Type**: using_directive\n\n```..."}, {"query": "namespace std", "results_count": 3, "preview": "🔍 **Codebase**: z80emu\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -0.623)\n📁 **File**: `Essenbee.Z80.Tests/SixteenBitLoadGroupShould.cs` | **Language**: CSHARP | **Type**: namespace\n\n..."}, {"query": "malloc free", "results_count": 3, "preview": "🔍 **Codebase**: z80emu\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -0.593)\n📁 **File**: `Essenbee.Z80.Tests/JumpGroupShould.cs` | **Language**: CSHARP | **Type**: method `mpGroupShould..."}], "success": true}, "ai_test": {"questions_tested": 3, "successful_answers": 3, "results": [{"question": "What encryption functions are available?", "success": true, "response_length": 1753}, {"question": "How is memory management handled?", "success": true, "response_length": 1781}, {"question": "What are the main classes and their purposes?", "success": true, "response_length": 1305}]}, "performance": {"average_time": 0.10096926689147949, "min_time": 0.05021929740905762, "max_time": 0.23764252662658691, "queries_tested": 5}}, {"collection_name": "modbus", "basic_test": {"collection_name": "modbus", "queries_tested": 10, "successful_queries": 10, "failed_queries": 0, "query_results": [{"query": "function definition", "results_count": 0, "preview": "❌ Error searching code: Embedding dimension 384 does not match collection dimensionality 768"}, {"query": "class declaration", "results_count": 0, "preview": "❌ Error searching code: Embedding dimension 384 does not match collection dimensionality 768"}, {"query": "error handling", "results_count": 0, "preview": "❌ Error searching code: Embedding dimension 384 does not match collection dimensionality 768"}, {"query": "memory allocation", "results_count": 0, "preview": "❌ Error searching code: Embedding dimension 384 does not match collection dimensionality 768"}, {"query": "data structure", "results_count": 0, "preview": "❌ Error searching code: Embedding dimension 384 does not match collection dimensionality 768"}, {"query": "pointer arithmetic", "results_count": 0, "preview": "❌ Error searching code: Embedding dimension 384 does not match collection dimensionality 768"}, {"query": "struct definition", "results_count": 0, "preview": "❌ Error searching code: Embedding dimension 384 does not match collection dimensionality 768"}, {"query": "template class", "results_count": 0, "preview": "❌ Error searching code: Embedding dimension 384 does not match collection dimensionality 768"}, {"query": "namespace std", "results_count": 0, "preview": "❌ Error searching code: Embedding dimension 384 does not match collection dimensionality 768"}, {"query": "malloc free", "results_count": 0, "preview": "❌ Error searching code: Embedding dimension 384 does not match collection dimensionality 768"}], "success": true}, "ai_test": {"questions_tested": 3, "successful_answers": 3, "results": [{"question": "What encryption functions are available?", "success": true, "response_length": 95}, {"question": "How is memory management handled?", "success": true, "response_length": 95}, {"question": "What are the main classes and their purposes?", "success": true, "response_length": 95}]}, "performance": {"average_time": 0.05469355583190918, "min_time": 0.039867401123046875, "max_time": 0.06883502006530762, "queries_tested": 5}}, {"collection_name": "networking_project", "basic_test": {"collection_name": "networking_project", "queries_tested": 10, "successful_queries": 10, "failed_queries": 0, "query_results": [{"query": "function definition", "results_count": 3, "preview": "🔍 **Codebase**: networking_project\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -0.248)\n📁 **File**: `socket_manager.cpp` | **Language**: CPP | **Type**: function `unknown()`\n\n```cpp\nFi..."}, {"query": "class declaration", "results_count": 3, "preview": "🔍 **Codebase**: networking_project\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -0.268)\n📁 **File**: `socket_manager.cpp` | **Language**: CPP | **Type**: struct_specifier\n\n```cpp\nFile: ..."}, {"query": "error handling", "results_count": 3, "preview": "🔍 **Codebase**: networking_project\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -0.653)\n📁 **File**: `socket_manager.cpp` | **Language**: CPP | **Type**: class `SocketManager`\n\n```cpp\nF..."}, {"query": "memory allocation", "results_count": 3, "preview": "🔍 **Codebase**: networking_project\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -0.259)\n📁 **File**: `buffer_pool.h` | **Language**: C | **Type**: function `unknown()`\n\n```c\nFile: buffe..."}, {"query": "data structure", "results_count": 3, "preview": "🔍 **Codebase**: networking_project\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -0.671)\n📁 **File**: `socket_manager.cpp` | **Language**: CPP | **Type**: struct_specifier\n\n```cpp\nFile: ..."}, {"query": "pointer arithmetic", "results_count": 3, "preview": "🔍 **Codebase**: networking_project\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -0.511)\n📁 **File**: `buffer_pool.h` | **Language**: C | **Type**: header\n\n```c\nFile: buffer_pool.h | Lan..."}, {"query": "struct definition", "results_count": 3, "preview": "🔍 **Codebase**: networking_project\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -0.200)\n📁 **File**: `socket_manager.cpp` | **Language**: CPP | **Type**: struct_specifier\n\n```cpp\nFile: ..."}, {"query": "template class", "results_count": 3, "preview": "🔍 **Codebase**: networking_project\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -0.497)\n📁 **File**: `socket_manager.cpp` | **Language**: CPP | **Type**: struct_specifier\n\n```cpp\nFile: ..."}, {"query": "namespace std", "results_count": 3, "preview": "🔍 **Codebase**: networking_project\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -0.280)\n📁 **File**: `socket_manager.cpp` | **Language**: CPP | **Type**: function `unknown()`\n\n```cpp\nFi..."}, {"query": "malloc free", "results_count": 3, "preview": "🔍 **Codebase**: networking_project\n🔍 Found 3 relevant code snippets:\n\n\n**Result 1** (Relevance: -0.484)\n📁 **File**: `socket_manager.cpp` | **Language**: CPP | **Type**: function `unknown()`\n\n```cpp\nFi..."}], "success": true}, "ai_test": {"questions_tested": 3, "successful_answers": 3, "results": [{"question": "What encryption functions are available?", "success": true, "response_length": 1792}, {"question": "How is memory management handled?", "success": true, "response_length": 1475}, {"question": "What are the main classes and their purposes?", "success": true, "response_length": 1665}]}, "performance": {"average_time": 0.05458359718322754, "min_time": 0.03796505928039551, "max_time": 0.0638427734375, "queries_tested": 5}}]}